import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>ton, Card, CardBody, CardHeader, Modal, ModalContent, ModalHeader, ModalBody, ModalFooter } from '@heroui/react';

/**
 * VentureReviewScreen Component
 * 
 * Final review screen showing venture configuration and agreement preview
 * Follows wireframe design with comprehensive review and agreement generation
 */
const VentureReviewScreen = ({
  questionData,
  projectData,
  generatedAgreement,
  onConfirm,
  onBack,
  onEditDetails,
  isLoading
}) => {
  const [agreementPreview, setAgreementPreview] = useState(null);
  const [showFullAgreement, setShowFullAgreement] = useState(false);
  const [validationStatus, setValidationStatus] = useState({
    legal: false,
    compliance: false,
    revenue: false,
    roles: false
  });

  // Generate agreement preview
  useEffect(() => {
    generateAgreementPreview();
    validateConfiguration();
  }, [questionData, projectData]);

  const generateAgreementPreview = async () => {
    if (generatedAgreement) {
      // Extract key information from the generated legal agreement
      const preview = {
        title: `${questionData.ventureName} - Collaboration Agreement`,
        parties: 'City of Gamers Inc. & Contributors',
        projectType: getProjectTypeDisplay(),
        revenueModel: projectData.royalty_model?.model_type?.replace('_', ' ') || 'Custom',
        ipOwnership: projectData.ip_ownership || 'Shared',
        pages: Math.ceil(generatedAgreement.length / 3000), // Estimate pages
        template: 'City of Gamers Legal Template',
        hasFullAgreement: true
      };
      setAgreementPreview(preview);
    } else {
      // Fallback preview if agreement generation failed
      const preview = {
        title: `${questionData.ventureName} Development Venture`,
        parties: 'Alliance Members',
        projectType: getProjectTypeDisplay(),
        revenueModel: 'CoG (Tasks-Time-Difficulty)',
        ipOwnership: 'Shared among contributors',
        pages: 12,
        template: getTemplateType(),
        hasFullAgreement: false
      };
      setAgreementPreview(preview);
    }
  };

  const validateConfiguration = () => {
    // Simulate validation checks
    setTimeout(() => {
      setValidationStatus({
        legal: true,
        compliance: true,
        revenue: true,
        roles: true
      });
    }, 1000);
  };

  const getProjectTypeDisplay = () => {
    const category = questionData.projectCategory;
    const subtype = questionData.projectSubtype;
    
    if (category === 'software' && subtype) {
      const subtypeMap = {
        'mobile': 'Mobile App',
        'web': 'Web Application',
        'desktop': 'Desktop Software',
        'tool': 'Developer Tool',
        'game': 'Game'
      };
      return subtypeMap[subtype] || 'Software';
    }
    
    const categoryMap = {
      'software': 'Software Development',
      'creative': 'Creative Work',
      'business': 'Business Service',
      'physical': 'Physical Product',
      'other': 'Custom Project'
    };
    
    return categoryMap[category] || 'Project';
  };

  const getTemplateType = () => {
    if (questionData.budget === 'funded') return 'Funded Startup - Professional';
    if (questionData.budget === 'revenue_first') return 'Revenue Share - Standard';
    return 'Small Business Compliant';
  };

  const handleViewFullAgreement = () => {
    setShowFullAgreement(true);
  };

  const handleDownloadAgreement = () => {
    if (generatedAgreement) {
      const blob = new Blob([generatedAgreement], { type: 'text/markdown' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${questionData.ventureName.replace(/\s+/g, '-')}-agreement.md`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  const getTimelineDisplay = () => {
    const timelineMap = {
      'quick': 'Quick Sprint (1-4 weeks)',
      'short': 'Short Project (1-3 months)',
      'medium': 'Medium Project (3-12 months)',
      'long': 'Long-term Vision (1+ years)',
      'flexible': 'Flexible Timeline'
    };
    return timelineMap[questionData.timeline] || 'Custom Timeline';
  };

  const getBudgetDisplay = () => {
    const budgetMap = {
      'funded': 'We have funding',
      'bootstrapped': 'Bootstrapped',
      'sweat_equity': 'Sweat Equity',
      'revenue_first': 'Revenue-First',
      'seeking_investment': 'Seeking Investment'
    };
    return budgetMap[questionData.budget] || 'Custom Budget';
  };

  const getSuccessDisplay = () => {
    const successMap = {
      'revenue': 'Revenue Target',
      'users': 'User Growth',
      'features': 'Feature Completion',
      'recognition': 'Recognition',
      'satisfaction': 'Personal Satisfaction'
    };
    return successMap[questionData.successMetrics] || 'Custom Success Metrics';
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4 }
    }
  };

  return (
    <motion.div
      className="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 flex items-center justify-center p-4"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <div className="max-w-4xl mx-auto w-full">
        {/* Header */}
        <motion.div variants={itemVariants} className="text-center mb-8">
          <h1 className="text-5xl font-bold text-white mb-4">
            🚀 Review Your Venture
          </h1>
          <p className="text-xl text-white/80">
            Everything looks great! Ready to create your venture?
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Venture Summary */}
          <motion.div variants={itemVariants}>
            <Card className="bg-white/10 backdrop-blur-md border border-white/20">
              <CardHeader>
                <h2 className="text-2xl font-semibold text-white">
                  Venture Summary
                </h2>
              </CardHeader>
              <CardBody className="space-y-4 text-white">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{questionData.ventureIcon}</span>
                  <div>
                    <div className="font-semibold text-lg">{questionData.ventureName}</div>
                    <div className="text-white/70 text-sm">{getProjectTypeDisplay()}</div>
                  </div>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div><strong>Timeline:</strong> {getTimelineDisplay()}</div>
                  <div><strong>Budget:</strong> {getBudgetDisplay()}</div>
                  <div><strong>Success:</strong> {getSuccessDisplay()}</div>
                  <div><strong>Target:</strong> {questionData.targetAudience}</div>
                </div>

                <div className="pt-2">
                  <div className="text-sm text-white/70 mb-2">Description:</div>
                  <div className="text-sm">{questionData.ventureDescription}</div>
                </div>

                {questionData.ventureTags.length > 0 && (
                  <div className="pt-2">
                    <div className="text-sm text-white/70 mb-2">Tags:</div>
                    <div className="flex flex-wrap gap-1">
                      {questionData.ventureTags.map((tag, index) => (
                        <span
                          key={index}
                          className="bg-white/20 px-2 py-1 rounded text-xs"
                        >
                          #{tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </CardBody>
            </Card>
          </motion.div>

          {/* Agreement Preview */}
          <motion.div variants={itemVariants}>
            <Card className="bg-white/10 backdrop-blur-md border border-white/20">
              <CardHeader>
                <h2 className="text-2xl font-semibold text-white">
                  Generated Agreement
                </h2>
              </CardHeader>
              <CardBody className="space-y-4 text-white">
                {agreementPreview ? (
                  <>
                    <div className="bg-white/10 rounded-lg p-4 border border-white/20">
                      <div className="font-semibold mb-2">COLLABORATION AGREEMENT</div>
                      <div className="text-sm space-y-1 text-white/80">
                        <div>{agreementPreview.title}</div>
                        <div>Parties: {agreementPreview.parties}</div>
                        <div>Project: {agreementPreview.projectType}</div>
                        <div>Revenue Model: {agreementPreview.revenueModel}</div>
                        <div>IP Ownership: {agreementPreview.ipOwnership}</div>
                      </div>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div><strong>🔧 Agreement Generator:</strong> Legal Template Engine</div>
                      <div><strong>📋 Template:</strong> {agreementPreview.template}</div>
                      <div><strong>⚖️ Legal Framework:</strong> Florida Corporation Law</div>
                      <div><strong>📄 Document Length:</strong> {agreementPreview.pages} pages</div>
                      <div><strong>🏛️ Legal Status:</strong> {agreementPreview.hasFullAgreement ? 'Lawyer-Approved Format' : 'Template Preview'}</div>
                      <div><strong>🤝 Agreement Purpose:</strong> For your venture contributors</div>
                    </div>

                    <div className="flex space-x-2">
                      {agreementPreview.hasFullAgreement ? (
                        <>
                          <Button
                            size="sm"
                            variant="bordered"
                            className="text-white border-white/30"
                            onPress={handleViewFullAgreement}
                          >
                            View Full Agreement
                          </Button>
                          <Button
                            size="sm"
                            variant="bordered"
                            className="text-white border-white/30"
                            onPress={handleDownloadAgreement}
                          >
                            Download PDF
                          </Button>
                        </>
                      ) : (
                        <Button size="sm" variant="bordered" className="text-white border-white/30" disabled>
                          Agreement Generation Failed
                        </Button>
                      )}
                    </div>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
                    <div className="text-white/70">Generating agreement...</div>
                  </div>
                )}
              </CardBody>
            </Card>
          </motion.div>
        </div>

        {/* Smart Configuration Summary */}
        <motion.div variants={itemVariants} className="mt-8">
          <Card className="bg-white/10 backdrop-blur-md border border-white/20">
            <CardHeader className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-white">
                🤖 Smart Configuration Applied
              </h2>
              {onEditDetails && (
                <Button
                  size="sm"
                  variant="bordered"
                  onPress={onEditDetails}
                  className="text-white border-white/30 hover:bg-white/10"
                >
                  Customize Details
                </Button>
              )}
            </CardHeader>
            <CardBody>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-white text-sm">
                <div className="space-y-3">
                  <div>
                    <div className="font-medium text-white/90 mb-2">💰 Revenue Model</div>
                    <div className="text-white/70">
                      {projectData.royalty_model?.model_type === 'custom' ? 'CoG Model' : 'Equal Split'} -
                      Optimized for {getBudgetDisplay().toLowerCase()} approach
                    </div>
                  </div>
                  <div>
                    <div className="font-medium text-white/90 mb-2">📊 Revenue Tranches</div>
                    <div className="text-white/70">
                      {projectData.revenue_tranches?.length || 0} tranches configured based on timeline
                    </div>
                  </div>
                  <div>
                    <div className="font-medium text-white/90 mb-2">📋 Task Types</div>
                    <div className="text-white/70">
                      {projectData.contribution_tracking?.task_types?.length || 0} task types for {getProjectTypeDisplay().toLowerCase()}
                    </div>
                  </div>
                </div>
                <div className="space-y-3">
                  <div>
                    <div className="font-medium text-white/90 mb-2">🎯 Milestones</div>
                    <div className="text-white/70">
                      {projectData.milestones?.length || 0} milestones based on {getTimelineDisplay().toLowerCase()}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium text-white/90 mb-2">📈 Success Metrics</div>
                    <div className="text-white/70">
                      Tracking {getSuccessDisplay().toLowerCase()} with smart targets
                    </div>
                  </div>
                  <div>
                    <div className="font-medium text-white/90 mb-2">⚖️ Legal Framework</div>
                    <div className="text-white/70">
                      {getTemplateType()} template with {agreementPreview?.pages || 12} pages
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-white/5 rounded-lg border border-white/10">
                <div className="flex items-start space-x-3">
                  <span className="text-blue-400 text-lg">💡</span>
                  <div className="text-white/80 text-sm">
                    <div className="font-medium mb-1">Smart Configuration Complete</div>
                    <div>
                      Based on your answers, we've automatically configured all the technical details.
                      You can customize any of these settings using the "Customize Details" button above,
                      or proceed to create your venture with these smart defaults.
                    </div>
                  </div>
                </div>
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Validation Status */}
        <motion.div variants={itemVariants} className="mt-6">
          <Card className="bg-white/10 backdrop-blur-md border border-white/20">
            <CardBody>
              <h3 className="text-lg font-semibold text-white mb-4">🔍 Validation Status</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                {Object.entries(validationStatus).map(([key, status]) => (
                  <div key={key} className="flex items-center space-x-2">
                    <span className={status ? "text-green-400" : "text-yellow-400"}>
                      {status ? "✅" : "⏳"}
                    </span>
                    <span className="text-white capitalize">
                      {key === 'legal' && 'Legal terms validated'}
                      {key === 'compliance' && 'Compliance checked'}
                      {key === 'revenue' && 'Revenue model verified'}
                      {key === 'roles' && 'Role assignments confirmed'}
                    </span>
                  </div>
                ))}
              </div>
            </CardBody>
          </Card>
        </motion.div>

        {/* Action Buttons */}
        <motion.div variants={itemVariants} className="mt-8 flex justify-between items-center">
          <div className="flex space-x-3">
            <Button
              size="lg"
              variant="bordered"
              onPress={onBack}
              disabled={isLoading}
              className="text-white border-white/30 hover:bg-white/10"
            >
              ← Back to Questions
            </Button>

            {onEditDetails && (
              <Button
                size="lg"
                variant="bordered"
                onPress={onEditDetails}
                disabled={isLoading}
                className="text-white border-white/30 hover:bg-white/10"
              >
                🔧 Customize Details
              </Button>
            )}
          </div>

          <Button
            size="lg"
            onPress={onConfirm}
            disabled={isLoading || !Object.values(validationStatus).every(Boolean)}
            className="bg-white text-primary font-semibold px-12 py-4 text-lg hover:bg-white/90"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary mr-2"></div>
                Creating Venture...
              </>
            ) : (
              'Create Venture 🚀'
            )}
          </Button>
        </motion.div>

        {/* Footer Note */}
        <motion.div variants={itemVariants} className="mt-6 text-center">
          <p className="text-white/60 text-sm">
            Agreement can be reviewed and modified after creation.
          </p>
        </motion.div>
      </div>

      {/* Full Agreement Modal */}
      <Modal
        isOpen={showFullAgreement}
        onClose={() => setShowFullAgreement(false)}
        size="5xl"
        scrollBehavior="inside"
      >
        <ModalContent>
          <ModalHeader className="flex flex-col gap-1">
            <h2 className="text-2xl font-bold">Legal Collaboration Agreement</h2>
            <p className="text-sm text-default-500">{questionData.ventureName}</p>
            <p className="text-xs text-default-400">
              Use this agreement when adding contributors to your venture
            </p>
          </ModalHeader>
          <ModalBody>
            {generatedAgreement ? (
              <div className="prose prose-sm max-w-none">
                <pre className="whitespace-pre-wrap font-mono text-sm bg-default-50 p-4 rounded-lg overflow-auto">
                  {generatedAgreement}
                </pre>
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-default-500">No agreement available to display.</p>
              </div>
            )}
          </ModalBody>
          <ModalFooter>
            <Button
              color="danger"
              variant="light"
              onPress={() => setShowFullAgreement(false)}
            >
              Close
            </Button>
            <Button
              color="primary"
              onPress={handleDownloadAgreement}
              disabled={!generatedAgreement}
            >
              Download Agreement
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </motion.div>
  );
};

export default VentureReviewScreen;
