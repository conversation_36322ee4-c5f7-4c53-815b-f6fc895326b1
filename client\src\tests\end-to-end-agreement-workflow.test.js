/**
 * End-to-End Agreement Workflow Tests
 * 
 * Tests the complete workflow from alliance creation → venture setup → 
 * contributor addition → agreement generation, and compares generated 
 * agreements against the lawyer-approved template structure.
 */

import { describe, test, expect, beforeEach } from 'vitest';
import { dynamicContributorManagement, REVENUE_MODEL_PRESETS } from '../utils/agreement/dynamicContributorManagement.js';
import fs from 'fs';
import path from 'path';

describe('End-to-End Agreement Workflow Tests', () => {
  
  let lawyerApprovedTemplate;
  
  beforeEach(async () => {
    // Clear any existing data
    dynamicContributorManagement.ventures.clear();
    dynamicContributorManagement.tranches.clear();
    dynamicContributorManagement.revenueDistributions.clear();
    
    // Load lawyer-approved template for comparison
    const templatePath = path.join(process.cwd(), 'public', 'example-cog-contributor-agreement.md');
    lawyerApprovedTemplate = fs.readFileSync(templatePath, 'utf8');
  });

  // ============================================================================
  // COMPLETE WORKFLOW TESTS
  // ============================================================================

  describe('Complete Venture-to-Agreement Workflow', () => {
    test('Should complete full workflow: Alliance → Venture → Contributors → Agreements', async () => {
      // 1. SIMULATE ALLIANCE CREATION
      const allianceData = {
        id: 'alliance_e2e_001',
        name: 'Tech Innovation Alliance',
        industry: 'TECHNOLOGY',
        description: 'Software development collaboration alliance',
        revenueModel: 'UNIFIED_POOL',
        ipOwnershipModel: 'CO_OWNERSHIP',
        jurisdiction: 'Delaware',
        currency: 'USD'
      };

      // 2. CREATE VENTURE WITH UNIFIED POOL (DEFAULT)
      const venture = dynamicContributorManagement.initializeScalableVenture({
        name: 'CloudSync Enterprise',
        description: 'Enterprise cloud synchronization platform with real-time collaboration',
        allianceId: allianceData.id,
        
        // Test default unified pool configuration
        coreTeam: [
          {
            email: '<EMAIL>',
            role: 'Technical Co-Founder & CTO',
            responsibilities: [
              'Technical architecture and system design',
              'Team leadership and code review',
              'Product development strategy'
            ],
            ipRights: 'co_owner'
          },
          {
            email: '<EMAIL>',
            role: 'Business Co-Founder & CEO',
            responsibilities: [
              'Business strategy and market analysis',
              'Customer development and sales',
              'Fundraising and investor relations'
            ],
            ipRights: 'co_owner'
          }
        ],
        autoStartFirstTranche: true
      });

      expect(venture).toBeDefined();
      expect(venture.revenueModel.calculationMethod).toBe('contribution_points');
      expect(venture.revenueModel.gigworkPoolPercentage).toBe(90);
      expect(venture.contributorPools.coreTeam).toHaveLength(2);

      // 3. ADD DIVERSE GIGWORK CONTRIBUTORS
      const contributors = [];

      // Senior Full-Stack Developer
      contributors.push(dynamicContributorManagement.addGigworkContributor(venture.id, {
        email: '<EMAIL>',
        role: 'Senior Full-Stack Developer',
        skills: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'Docker'],
        experienceLevel: 'senior',
        platformRating: 4.9,
        completedProjects: 28,
        responsibilities: [
          'Frontend application development',
          'Backend API development',
          'Database optimization and scaling'
        ],
        participationModel: 'tranche_based'
      }));

      // UX/UI Designer
      contributors.push(dynamicContributorManagement.addGigworkContributor(venture.id, {
        email: '<EMAIL>',
        role: 'Senior UX/UI Designer',
        skills: ['Figma', 'User Research', 'Prototyping', 'Design Systems'],
        experienceLevel: 'senior',
        platformRating: 4.8,
        completedProjects: 22,
        responsibilities: [
          'User experience research and design',
          'Interface design and prototyping',
          'Design system development'
        ],
        participationModel: 'milestone_based'
      }));

      // DevOps Engineer
      contributors.push(dynamicContributorManagement.addGigworkContributor(venture.id, {
        email: '<EMAIL>',
        role: 'DevOps Engineer',
        skills: ['AWS', 'Kubernetes', 'CI/CD', 'Terraform', 'Monitoring'],
        experienceLevel: 'expert',
        platformRating: 4.9,
        completedProjects: 35,
        responsibilities: [
          'Infrastructure setup and management',
          'CI/CD pipeline development',
          'Security and monitoring implementation'
        ],
        participationModel: 'continuous'
      }));

      expect(contributors).toHaveLength(3);
      expect(venture.contributorPools.gigwork).toHaveLength(3);

      // 4. RECORD REALISTIC CONTRIBUTIONS
      const tranche = Array.from(dynamicContributorManagement.tranches.values())[0];
      const allContributors = Object.values(venture.contributorPools).flat();

      const contributionData = [
        // Founders working hard
        {
          contributorEmail: '<EMAIL>',
          type: 'code',
          description: 'Core architecture, API design, and technical leadership',
          hoursWorked: 120,
          difficultyLevel: 'expert',
          qualityRating: 5
        },
        {
          contributorEmail: '<EMAIL>',
          type: 'planning',
          description: 'Business strategy, customer development, and product planning',
          hoursWorked: 80,
          difficultyLevel: 'hard',
          qualityRating: 5
        },
        // Gigwork contributors
        {
          contributorEmail: '<EMAIL>',
          type: 'code',
          description: 'Frontend React components and backend API development',
          hoursWorked: 100,
          difficultyLevel: 'hard',
          qualityRating: 4
        },
        {
          contributorEmail: '<EMAIL>',
          type: 'design',
          description: 'Complete UX research, UI design, and design system',
          hoursWorked: 70,
          difficultyLevel: 'hard',
          qualityRating: 5
        },
        {
          contributorEmail: '<EMAIL>',
          type: 'code',
          description: 'Infrastructure setup, CI/CD, and security implementation',
          hoursWorked: 60,
          difficultyLevel: 'expert',
          qualityRating: 5
        }
      ];

      let totalContributionPoints = 0;
      contributionData.forEach(contrib => {
        const contributor = allContributors.find(c => c.email === contrib.contributorEmail);
        if (contributor) {
          const contribution = dynamicContributorManagement.recordContribution(
            tranche.id,
            contributor.id,
            contrib
          );
          totalContributionPoints += contribution.finalPoints;
        }
      });

      expect(totalContributionPoints).toBeGreaterThan(0);

      // 5. CALCULATE REVENUE DISTRIBUTION
      const trancheRevenue = 250000; // $250k revenue
      const revenueDistribution = dynamicContributorManagement.calculateTrancheRevenueDistribution(
        tranche.id,
        trancheRevenue
      );

      expect(revenueDistribution.totalRevenue).toBe(trancheRevenue);
      expect(revenueDistribution.platformFee).toBe(25000); // 10%
      expect(revenueDistribution.gigworkPool).toBe(225000); // 90%
      expect(Object.keys(revenueDistribution.gigworkDistribution)).toHaveLength(5); // All contributors

      // 6. GENERATE AGREEMENTS FOR ALL CONTRIBUTORS
      const generatedAgreements = [];
      
      contributors.forEach(contributor => {
        const agreement = dynamicContributorManagement.generateGigworkAgreement(
          venture.id,
          contributor.id
        );
        generatedAgreements.push({
          contributor: contributor.email,
          role: contributor.role,
          agreement
        });
      });

      expect(generatedAgreements).toHaveLength(3);

      // 7. VALIDATE AGREEMENT STRUCTURE AND CONTENT
      generatedAgreements.forEach(({ contributor, role, agreement }) => {
        // Basic structure validation
        expect(agreement.id).toBeDefined();
        expect(agreement.ventureId).toBe(venture.id);
        expect(agreement.contributorId).toBeDefined();
        expect(agreement.content).toBeDefined();
        expect(agreement.content.length).toBeGreaterThan(500);

        // Unified pool specific validation
        expect(agreement.terms.revenueSharing.isUnifiedPool).toBe(true);
        expect(agreement.terms.revenueSharing.contributorPoolPercentage).toBe(90);
        expect(agreement.content).toContain('Unified Contributor Pool');
        expect(agreement.content).toContain('ALL contributors');
        expect(agreement.content).toContain('pure meritocracy');

        // Professional content validation
        expect(agreement.content).toContain('DYNAMIC CONTRIBUTOR AGREEMENT');
        expect(agreement.content).toContain(venture.name);
        expect(agreement.content).toContain(contributor);
        expect(agreement.content).toContain('INTELLECTUAL PROPERTY');
        expect(agreement.content).toContain('REVENUE SHARING');
      });

      // 8. WORKFLOW COMPLETION VALIDATION
      expect(venture.status).toBeDefined();
      expect(venture.activeTranches).toHaveLength(1);
      expect(Object.values(venture.contributorPools).flat()).toHaveLength(5);
      expect(revenueDistribution.participantCount).toBe(5);
      expect(generatedAgreements).toHaveLength(3);
    });
  });

  // ============================================================================
  // AGREEMENT COMPARISON TESTS
  // ============================================================================

  describe('Agreement Comparison Against Lawyer-Approved Template', () => {
    test('Should analyze structure differences between generated and lawyer-approved agreements', () => {
      // Create a simple venture for agreement generation
      const venture = dynamicContributorManagement.initializeScalableVenture({
        name: 'Test Venture for Comparison',
        coreTeam: [{
          email: '<EMAIL>',
          role: 'Founder'
        }]
      });

      const contributor = dynamicContributorManagement.addGigworkContributor(venture.id, {
        email: '<EMAIL>',
        role: 'Developer'
      });

      const generatedAgreement = dynamicContributorManagement.generateGigworkAgreement(
        venture.id,
        contributor.id
      );

      // STRUCTURE ANALYSIS
      const lawyerSections = extractSections(lawyerApprovedTemplate);
      const generatedSections = extractSections(generatedAgreement.content);

      console.log('\n📊 AGREEMENT STRUCTURE COMPARISON:');
      console.log('Lawyer-Approved Template Sections:', lawyerSections.length);
      console.log('Generated Agreement Sections:', generatedSections.length);

      // KEY SECTIONS THAT SHOULD BE PRESENT
      const criticalSections = [
        'REVENUE SHARING',
        'INTELLECTUAL PROPERTY',
        'CONFIDENTIALITY',
        'TERMINATION',
        'PARTICIPATION'
      ];

      console.log('\n📊 CRITICAL SECTIONS ANALYSIS:');
      const missingSections = [];
      criticalSections.forEach(section => {
        const inGenerated = generatedAgreement.content.includes(section);
        console.log(`${section}: ${inGenerated ? '✅' : '❌'} in generated`);
        if (!inGenerated) {
          missingSections.push(section);
        }
      });

      // Test with enhanced generator
      const enhancedAgreement = dynamicContributorManagement.generateGigworkAgreement(
        venture.id,
        contributor.id,
        { useEnhancedGenerator: true }
      );

      console.log('\n📊 ENHANCED AGREEMENT SECTIONS:');
      criticalSections.forEach(section => {
        const inEnhanced = enhancedAgreement.content.includes(section);
        console.log(`${section}: ${inEnhanced ? '✅' : '❌'} in enhanced`);
      });

      // At least some sections should be present in enhanced version
      const enhancedSectionsPresent = criticalSections.filter(section =>
        enhancedAgreement.content.includes(section)
      ).length;

      expect(enhancedSectionsPresent).toBeGreaterThan(2); // At least 3 sections should be present

      // CONTENT QUALITY VALIDATION
      expect(generatedAgreement.content.length).toBeGreaterThan(500);
      expect(generatedAgreement.content).toContain('Agreement');
      expect(generatedAgreement.content).toContain(venture.name);
      expect(generatedAgreement.content).toContain(contributor.email);
    });

    test('Should identify missing legal provisions compared to lawyer template', () => {
      const venture = dynamicContributorManagement.initializeScalableVenture({
        name: 'Legal Comparison Venture',
        coreTeam: [{ email: '<EMAIL>', role: 'Founder' }]
      });

      const contributor = dynamicContributorManagement.addGigworkContributor(venture.id, {
        email: '<EMAIL>',
        role: 'Developer'
      });

      const generatedAgreement = dynamicContributorManagement.generateGigworkAgreement(
        venture.id,
        contributor.id
      );

      // LEGAL PROVISIONS FROM LAWYER TEMPLATE
      const lawyerProvisions = [
        'Definitions',
        'Treatment of Confidential Information',
        'Ownership of Work Product',
        'Non-Disparagement',
        'Termination',
        'Equitable Remedies',
        'Assignment',
        'Waivers and Amendments',
        'Survival',
        'Status as Independent Contractor',
        'Representations and Warranties',
        'Indemnification',
        'Entire Agreement',
        'Governing Law',
        'Consent to Jurisdiction',
        'Settlement of Disputes'
      ];

      console.log('\n⚖️ LEGAL PROVISIONS COMPARISON:');
      
      const missingProvisions = [];
      const presentProvisions = [];

      lawyerProvisions.forEach(provision => {
        const isPresent = generatedAgreement.content.toLowerCase().includes(provision.toLowerCase()) ||
                         generatedAgreement.content.includes(provision);
        
        if (isPresent) {
          presentProvisions.push(provision);
        } else {
          missingProvisions.push(provision);
        }
        
        console.log(`${provision}: ${isPresent ? '✅' : '❌'}`);
      });

      console.log(`\nPresent: ${presentProvisions.length}/${lawyerProvisions.length}`);
      console.log(`Missing: ${missingProvisions.length}/${lawyerProvisions.length}`);

      // Test enhanced agreement for better coverage
      const enhancedAgreement = dynamicContributorManagement.generateGigworkAgreement(
        venture.id,
        contributor.id,
        { useEnhancedGenerator: true }
      );

      console.log('\n⚖️ ENHANCED AGREEMENT LEGAL PROVISIONS:');
      const enhancedMissing = [];
      const enhancedPresent = [];

      lawyerProvisions.forEach(provision => {
        const isPresent = enhancedAgreement.content.toLowerCase().includes(provision.toLowerCase()) ||
                         enhancedAgreement.content.includes(provision);

        if (isPresent) {
          enhancedPresent.push(provision);
        } else {
          enhancedMissing.push(provision);
        }

        console.log(`${provision}: ${isPresent ? '✅' : '❌'}`);
      });

      console.log(`\nEnhanced Present: ${enhancedPresent.length}/${lawyerProvisions.length}`);
      console.log(`Enhanced Missing: ${enhancedMissing.length}/${lawyerProvisions.length}`);

      // Store results for iteration - enhanced should be better
      expect(enhancedPresent.length).toBeGreaterThan(presentProvisions.length); // Enhanced should be better
      expect(enhancedMissing.length).toBeLessThan(missingProvisions.length); // Enhanced should have fewer missing
    });
  });

  // ============================================================================
  // REVENUE MODEL COMPARISON TESTS
  // ============================================================================

  describe('Revenue Model Workflow Comparison', () => {
    test('Should test all three revenue models end-to-end', () => {
      const revenueModels = [
        { name: 'Unified Pool', preset: 'UNIFIED_POOL' },
        { name: 'Hybrid Safety Net', preset: 'HYBRID_SAFETY_NET' },
        { name: 'Separate Pools', preset: 'SEPARATE_POOLS' }
      ];

      const results = [];

      revenueModels.forEach(({ name, preset }) => {
        // Create venture with specific revenue model
        const venture = dynamicContributorManagement.initializeScalableVenture({
          name: `${name} Test Venture`,
          revenueModel: REVENUE_MODEL_PRESETS[preset],
          coreTeam: [{
            email: `founder@${preset.toLowerCase()}.com`,
            role: 'Founder',
            revenueShare: preset === 'UNIFIED_POOL' ? 0 : (preset === 'HYBRID_SAFETY_NET' ? 8 : 15)
          }],
          autoStartFirstTranche: true
        });

        // Add contributor
        const contributor = dynamicContributorManagement.addGigworkContributor(venture.id, {
          email: `contributor@${preset.toLowerCase()}.com`,
          role: 'Developer'
        });

        // Record contributions
        const tranche = Array.from(dynamicContributorManagement.tranches.values()).pop();
        const founder = venture.contributorPools.coreTeam[0];

        dynamicContributorManagement.recordContribution(tranche.id, founder.id, {
          type: 'code',
          hoursWorked: 40,
          difficultyLevel: 'expert',
          qualityRating: 5
        });

        dynamicContributorManagement.recordContribution(tranche.id, contributor.id, {
          type: 'code',
          hoursWorked: 30,
          difficultyLevel: 'hard',
          qualityRating: 4
        });

        // Calculate revenue
        const distribution = dynamicContributorManagement.calculateTrancheRevenueDistribution(
          tranche.id,
          100000
        );

        // Generate agreement
        const agreement = dynamicContributorManagement.generateGigworkAgreement(
          venture.id,
          contributor.id
        );

        results.push({
          model: name,
          venture,
          distribution,
          agreement,
          founderRevenue: distribution.coreTeamDistribution[founder.id] || distribution.gigworkDistribution[founder.id] || 0,
          contributorRevenue: distribution.gigworkDistribution[contributor.id] || 0
        });
      });

      // Validate all models work
      expect(results).toHaveLength(3);
      
      results.forEach(result => {
        expect(result.venture).toBeDefined();
        expect(result.distribution).toBeDefined();
        expect(result.agreement).toBeDefined();
        expect(result.agreement.content).toContain('DYNAMIC CONTRIBUTOR AGREEMENT');
      });

      // Compare revenue distributions
      console.log('\n💰 REVENUE MODEL COMPARISON:');
      results.forEach(result => {
        console.log(`${result.model}:`);
        console.log(`  Founder Revenue: $${result.founderRevenue.toLocaleString()}`);
        console.log(`  Contributor Revenue: $${result.contributorRevenue.toLocaleString()}`);
        console.log(`  Agreement Type: ${result.agreement.terms.revenueSharing.isUnifiedPool ? 'Unified Pool' : 'Separate Pools'}`);
      });
    });
  });
});

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

function extractSections(content) {
  // Extract sections marked with ## or numbered sections
  const sectionRegex = /(?:^|\n)(?:##\s+(.+)|(\d+)\.\s+(.+))/gm;
  const sections = [];
  let match;
  
  while ((match = sectionRegex.exec(content)) !== null) {
    sections.push(match[1] || match[3] || match[0].trim());
  }
  
  return sections;
}
