import { describe, it, expect, beforeAll } from 'vitest'
import fs from 'fs/promises'
import path from 'path'

import {
  softwareVentureAnswers,
  creativeVentureAnswers,
  businessVentureAnswers,
  startupVentureAnswers
} from '../mocks/venture-answers.js'

import { mapVentureAnswersToProjectData } from '../../../utils/venture-mapping.js'
import { generateLegalAgreement } from '../../../utils/legal-agreement-generator.js'

// Get current directory for file operations
const OUTPUT_DIR = path.resolve(process.cwd(), 'src/tests/venture/output')

/**
 * Generate a complete legal agreement document from project data
 */
function generateAgreementDocument(projectData, answers, scenarioName) {
  // Create venture owner info for the test scenario
  const ventureOwnerInfo = {
    companyName: projectData.name,
    ownerName: `[Venture Owner - ${scenarioName}]`,
    ownerTitle: 'Founder',
    address: '[Venture Address]',
    address2: '[City, State ZIP]',
    address3: '[Country]',
    state: 'Florida',
    entityType: 'LLC'
  }

  // Create contributor info for the test scenario
  const contributorInfo = {
    name: `[Test Contributor - ${scenarioName}]`,
    isCompany: false,
    address: '[Test Address]',
    address2: '[City, State ZIP]',
    address3: '[Country]'
  }

  // Generate the formal legal agreement
  const legalAgreement = generateLegalAgreement(projectData, answers, ventureOwnerInfo, contributorInfo)

  // Add test metadata at the top
  const testMetadata = `<!-- TEST METADATA
Generated: ${new Date().toISOString()}
Scenario: ${scenarioName}
Project: ${projectData.name}
Agreement Type: ${projectData.agreement_type}
Project Type: ${projectData.project_type}

Original Venture Answers:
- Name: ${answers.ventureName}
- Category: ${answers.projectCategory}
- Description: ${answers.ventureDescription}
- Timeline: ${answers.timeline}
- Target Audience: ${answers.targetAudience}
- Budget: ${answers.budget}
- Revenue Sharing: ${answers.revenueSharing}
- Success Metrics: ${answers.successMetrics}
- Tags: ${answers.ventureTags.join(', ')}
- Icon: ${answers.ventureIcon}
- Agreement Type: ${answers.agreementType}
- IP Ownership: ${answers.ipOwnership}
- Dispute Resolution: ${answers.disputeResolution}
-->

`

  return testMetadata + legalAgreement
}

/**
 * Save generated content to files
 */
async function saveTestOutput(scenarioName, projectData, agreement, answers) {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]
  const safeName = projectData.name.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()
  
  // Ensure directories exist
  const agreementDir = path.join(OUTPUT_DIR, 'agreements', scenarioName)
  const dataDir = path.join(OUTPUT_DIR, 'project-data', scenarioName)
  
  await fs.mkdir(agreementDir, { recursive: true })
  await fs.mkdir(dataDir, { recursive: true })
  
  // File paths
  const agreementPath = path.join(agreementDir, `${safeName}-${timestamp}.md`)
  const dataPath = path.join(dataDir, `${safeName}-${timestamp}.json`)
  const answersPath = path.join(dataDir, `${safeName}-answers-${timestamp}.json`)
  
  // Save files
  await fs.writeFile(agreementPath, agreement, 'utf8')
  await fs.writeFile(dataPath, JSON.stringify(projectData, null, 2), 'utf8')
  await fs.writeFile(answersPath, JSON.stringify(answers, null, 2), 'utf8')
  
  return {
    agreementPath,
    dataPath,
    answersPath
  }
}

describe('Agreement Generation Visibility Tests', () => {
  beforeAll(async () => {
    // Ensure output directory exists
    await fs.mkdir(OUTPUT_DIR, { recursive: true })
  })

  describe('Software Venture Agreement Generation', () => {
    it('generates complete agreement for TaskMaster Pro', async () => {
      const projectData = mapVentureAnswersToProjectData(softwareVentureAnswers)
      const agreement = generateAgreementDocument(projectData, softwareVentureAnswers, 'Software Venture')
      
      // Save for manual review
      const files = await saveTestOutput('software', projectData, agreement, softwareVentureAnswers)
      
      // Validate structure
      expect(projectData.name).toBe('TaskMaster Pro')
      expect(projectData.project_type).toBe('software')
      expect(projectData.royalty_model.model_type).toBe('contribution_based')
      expect(projectData.milestones.length).toBeGreaterThan(0)
      expect(agreement).toContain('TASKMASTER PRO')
      expect(agreement).toContain('CONTRIBUTOR AGREEMENT')
      expect(agreement).toContain('TaskMaster Pro')
      expect(agreement).toContain('SCHEDULE B')
      expect(agreement).toContain('Revenue Sharing Model')
      expect(agreement).toContain('EXHIBIT II')
      
      console.log(`📄 Software Agreement: ${files.agreementPath}`)
      console.log(`📊 Software Data: ${files.dataPath}`)
    })
  })

  describe('Creative Venture Agreement Generation', () => {
    it('generates complete agreement for Indie Music Collective', async () => {
      const projectData = mapVentureAnswersToProjectData(creativeVentureAnswers)
      const agreement = generateAgreementDocument(projectData, creativeVentureAnswers, 'Creative Venture')
      
      // Save for manual review
      const files = await saveTestOutput('creative', projectData, agreement, creativeVentureAnswers)
      
      // Validate structure
      expect(projectData.name).toBe('Indie Music Collective')
      expect(projectData.project_type).toBe('creative')
      expect(projectData.royalty_model.model_type).toBe('equal_split')
      expect(projectData.estimated_duration).toBe(12) // long timeline
      expect(agreement).toContain('INDIE MUSIC COLLECTIVE')
      expect(agreement).toContain('Indie Music Collective')
      expect(agreement).toContain('equal split')
      
      console.log(`📄 Creative Agreement: ${files.agreementPath}`)
      console.log(`📊 Creative Data: ${files.dataPath}`)
    })
  })

  describe('Business Venture Agreement Generation', () => {
    it('generates complete agreement for Digital Marketing Solutions', async () => {
      const projectData = mapVentureAnswersToProjectData(businessVentureAnswers)
      const agreement = generateAgreementDocument(projectData, businessVentureAnswers, 'Business Venture')
      
      // Save for manual review
      const files = await saveTestOutput('business', projectData, agreement, businessVentureAnswers)
      
      // Validate structure
      expect(projectData.name).toBe('Digital Marketing Solutions')
      expect(projectData.project_type).toBe('business')
      expect(projectData.royalty_model.model_type).toBe('investment_based')
      expect(projectData.estimated_duration).toBe(2) // short timeline
      expect(agreement).toContain('DIGITAL MARKETING SOLUTIONS')
      expect(agreement).toContain('Digital Marketing Solutions')
      expect(agreement).toContain('Client Acquisition')
      
      console.log(`📄 Business Agreement: ${files.agreementPath}`)
      console.log(`📊 Business Data: ${files.dataPath}`)
    })
  })

  describe('Startup Venture Agreement Generation', () => {
    it('generates complete agreement for EcoTech Innovations', async () => {
      const projectData = mapVentureAnswersToProjectData(startupVentureAnswers)
      const agreement = generateAgreementDocument(projectData, startupVentureAnswers, 'Startup Venture')
      
      // Save for manual review
      const files = await saveTestOutput('startup', projectData, agreement, startupVentureAnswers)
      
      // Validate structure
      expect(projectData.name).toBe('EcoTech Innovations')
      expect(projectData.project_type).toBe('software')
      expect(projectData.royalty_model.model_type).toBe('custom')
      expect(projectData.estimated_duration).toBe(12) // long timeline
      expect(agreement).toContain('ECOTECH INNOVATIONS')
      expect(agreement).toContain('EcoTech Innovations')
      expect(agreement).toContain('sustainability')
      
      console.log(`📄 Startup Agreement: ${files.agreementPath}`)
      console.log(`📊 Startup Data: ${files.dataPath}`)
    })
  })

  describe('Agreement Quality Validation', () => {
    it('validates all agreements have required sections', async () => {
      const scenarios = [
        { name: 'software', answers: softwareVentureAnswers },
        { name: 'creative', answers: creativeVentureAnswers },
        { name: 'business', answers: businessVentureAnswers },
        { name: 'startup', answers: startupVentureAnswers }
      ]

      for (const scenario of scenarios) {
        const projectData = mapVentureAnswersToProjectData(scenario.answers)
        const agreement = generateAgreementDocument(projectData, scenario.answers, scenario.name)

        // Check required legal sections
        expect(agreement).toContain('CONTRIBUTOR AGREEMENT')
        expect(agreement).toContain('Recitals')
        expect(agreement).toContain('Definitions')
        expect(agreement).toContain('SCHEDULE A')
        expect(agreement).toContain('SCHEDULE B')
        expect(agreement).toContain('EXHIBIT I')
        expect(agreement).toContain('EXHIBIT II')

        // Check data completeness
        expect(projectData.name).toBeTruthy()
        expect(projectData.description).toBeTruthy()
        expect(projectData.royalty_model).toBeTruthy()
        expect(projectData.milestones.length).toBeGreaterThan(0)
        expect(projectData.contribution_tracking.categories.length).toBeGreaterThan(0)
        expect(projectData.revenue_tranches.length).toBeGreaterThan(0)
      }
    })
  })
})
