import { describe, it, expect, vi } from 'vitest'
import { 
  softwareVentureAnswers, 
  creativeVentureAnswers, 
  businessVentureAnswers,
  startupVentureAnswers 
} from '../mocks/venture-answers'

// Import the mapping functions (we'll need to create these)
import { 
  mapVentureAnswersToProjectData,
  generateSmartDefaults,
  createRoyaltyModel,
  generateMilestones
} from '../../../utils/venture-mapping'

describe('Question to Project Data Mapping', () => {
  describe('Basic Mapping Functionality', () => {
    it('maps software venture answers to correct project data', () => {
      const projectData = mapVentureAnswersToProjectData(softwareVentureAnswers)
      
      expect(projectData).toEqual(expect.objectContaining({
        name: 'TaskMaster Pro',
        title: 'TaskMaster Pro',
        description: 'A mobile app that helps teams manage tasks and track productivity with smart automation',
        project_type: 'software',
        estimated_duration: 6, // medium timeline = 6 months
        target_audience: 'business',
        icon: '📱',
        tags: ['mobile', 'productivity', 'automation'],
        is_public: true
      }))
    })

    it('maps creative venture answers to correct project data', () => {
      const projectData = mapVentureAnswersToProjectData(creativeVentureAnswers)
      
      expect(projectData).toEqual(expect.objectContaining({
        name: 'Indie Music Collective',
        project_type: 'creative',
        estimated_duration: 12, // long timeline = 12 months
        target_audience: 'general',
        icon: '🎵',
        tags: ['music', 'creative', 'indie']
      }))
    })

    it('maps business venture answers to correct project data', () => {
      const projectData = mapVentureAnswersToProjectData(businessVentureAnswers)
      
      expect(projectData).toEqual(expect.objectContaining({
        name: 'Digital Marketing Solutions',
        project_type: 'business',
        estimated_duration: 2, // short timeline = 2 months
        target_audience: 'business',
        icon: '💼',
        tags: ['consulting', 'marketing', 'digital']
      }))
    })
  })

  describe('Smart Defaults Generation', () => {
    it('generates appropriate defaults for software projects', () => {
      const defaults = generateSmartDefaults(softwareVentureAnswers)
      
      expect(defaults).toEqual(expect.objectContaining({
        contribution_categories: expect.arrayContaining([
          'Development',
          'Design',
          'Testing',
          'Documentation'
        ]),
        milestone_templates: expect.arrayContaining([
          expect.objectContaining({ name: 'MVP Development' }),
          expect.objectContaining({ name: 'Beta Testing' }),
          expect.objectContaining({ name: 'Launch' })
        ]),
        revenue_tranches: expect.any(Array)
      }))
    })

    it('generates appropriate defaults for creative projects', () => {
      const defaults = generateSmartDefaults(creativeVentureAnswers)
      
      expect(defaults).toEqual(expect.objectContaining({
        contribution_categories: expect.arrayContaining([
          'Creative Work',
          'Production',
          'Marketing',
          'Distribution'
        ]),
        milestone_templates: expect.arrayContaining([
          expect.objectContaining({ name: 'Concept Development' }),
          expect.objectContaining({ name: 'Production' }),
          expect.objectContaining({ name: 'Release' })
        ])
      }))
    })

    it('generates appropriate defaults for business projects', () => {
      const defaults = generateSmartDefaults(businessVentureAnswers)
      
      expect(defaults).toEqual(expect.objectContaining({
        contribution_categories: expect.arrayContaining([
          'Strategy',
          'Implementation',
          'Client Management',
          'Business Development'
        ]),
        milestone_templates: expect.arrayContaining([
          expect.objectContaining({ name: 'Service Framework' }),
          expect.objectContaining({ name: 'Client Acquisition' }),
          expect.objectContaining({ name: 'Scale Operations' })
        ])
      }))
    })
  })

  describe('Royalty Model Creation', () => {
    it('creates contribution-based royalty model correctly', () => {
      const royaltyModel = createRoyaltyModel({
        revenueSharing: 'contribution',
        budget: 'bootstrapped',
        agreementType: 'business'
      })
      
      expect(royaltyModel).toEqual(expect.objectContaining({
        model_type: 'contribution_based',
        base_percentage: 0,
        contribution_multiplier: 1.0,
        minimum_threshold: 0,
        calculation_method: 'dynamic'
      }))
    })

    it('creates equal split royalty model correctly', () => {
      const royaltyModel = createRoyaltyModel({
        revenueSharing: 'equal',
        budget: 'sweat_equity',
        agreementType: 'simple'
      })
      
      expect(royaltyModel).toEqual(expect.objectContaining({
        model_type: 'equal_split',
        base_percentage: 50, // Will be divided among team members
        contribution_multiplier: 0,
        minimum_threshold: 0,
        calculation_method: 'fixed'
      }))
    })

    it('creates investment-based royalty model correctly', () => {
      const royaltyModel = createRoyaltyModel({
        revenueSharing: 'investment',
        budget: 'funded',
        agreementType: 'startup'
      })
      
      expect(royaltyModel).toEqual(expect.objectContaining({
        model_type: 'investment_based',
        base_percentage: 0,
        contribution_multiplier: 0.5,
        minimum_threshold: 1000,
        calculation_method: 'tiered'
      }))
    })
  })

  describe('Milestone Generation', () => {
    it('generates timeline-appropriate milestones for quick projects', () => {
      const milestones = generateMilestones({
        timeline: 'quick',
        projectCategory: 'software',
        successMetrics: 'features'
      })
      
      expect(milestones).toHaveLength(3) // Quick projects have fewer milestones
      expect(milestones[0]).toEqual(expect.objectContaining({
        name: expect.any(String),
        target_date: expect.any(Date),
        percentage: expect.any(Number)
      }))
    })

    it('generates timeline-appropriate milestones for long projects', () => {
      const milestones = generateMilestones({
        timeline: 'long',
        projectCategory: 'creative',
        successMetrics: 'satisfaction'
      })
      
      expect(milestones).toHaveLength(6) // Long projects have more milestones
      expect(milestones.every(m => m.percentage >= 0 && m.percentage <= 100)).toBe(true)
    })

    it('customizes milestones based on success metrics', () => {
      const revenueMilestones = generateMilestones({
        timeline: 'medium',
        projectCategory: 'business',
        successMetrics: 'revenue'
      })

      const userMilestones = generateMilestones({
        timeline: 'medium',
        projectCategory: 'software',
        successMetrics: 'users'
      })



      // Revenue-focused milestones should include business/financial targets
      // For business projects, this includes client acquisition and scaling
      expect(
        revenueMilestones.some(m =>
          m.name.toLowerCase().includes('revenue') ||
          m.name.toLowerCase().includes('client') ||
          m.name.toLowerCase().includes('scale')
        )
      ).toBe(true)

      // User-focused milestones should include user acquisition targets
      expect(userMilestones.some(m => m.name.toLowerCase().includes('user'))).toBe(true)
    })
  })

  describe('Complete Integration', () => {
    it('creates complete project data from venture answers', () => {
      const completeData = mapVentureAnswersToProjectData(startupVentureAnswers)
      
      // Should have all required fields for project creation
      expect(completeData).toEqual(expect.objectContaining({
        // Basic project info
        name: expect.any(String),
        title: expect.any(String),
        description: expect.any(String),
        project_type: expect.any(String),
        
        // Timeline and audience
        estimated_duration: expect.any(Number),
        target_audience: expect.any(String),
        
        // Visual and categorization
        icon: expect.any(String),
        tags: expect.any(Array),
        
        // Financial model
        royalty_model: expect.objectContaining({
          model_type: expect.any(String)
        }),
        
        // Project structure
        contribution_tracking: expect.objectContaining({
          categories: expect.any(Array)
        }),
        
        // Milestones and progress
        milestones: expect.any(Array),
        
        // Revenue structure
        revenue_tranches: expect.any(Array)
      }))
    })

    it('handles edge cases and missing data gracefully', () => {
      const incompleteAnswers = {
        ventureName: 'Test',
        projectCategory: 'software'
        // Missing many required fields
      }
      
      expect(() => {
        mapVentureAnswersToProjectData(incompleteAnswers)
      }).not.toThrow()
      
      const result = mapVentureAnswersToProjectData(incompleteAnswers)
      expect(result.name).toBe('Test')
      expect(result.project_type).toBe('software')
      // Should have sensible defaults for missing fields
      expect(result.estimated_duration).toBeGreaterThan(0)
    })
  })
})
