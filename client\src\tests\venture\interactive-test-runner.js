#!/usr/bin/env node

/**
 * Interactive Venture Testing Runner
 * 
 * Allows running specific venture scenarios and viewing generated agreements
 * Usage: node interactive-test-runner.js [scenario]
 */

import fs from 'fs/promises'
import path from 'path'
import { fileURLToPath } from 'url'

// Import our test data and mapping functions
import { 
  softwareVentureAnswers, 
  creativeVentureAnswers, 
  businessVentureAnswers,
  startupVentureAnswers,
  allAnswerCombinations 
} from './mocks/venture-answers.js'

import { mapVentureAnswersToProjectData } from '../../utils/venture-mapping.js'

// Get current directory
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Output directories
const OUTPUT_DIR = path.join(__dirname, 'output')
const AGREEMENTS_DIR = path.join(OUTPUT_DIR, 'agreements')
const PROJECT_DATA_DIR = path.join(OUTPUT_DIR, 'project-data')
const REPORTS_DIR = path.join(OUTPUT_DIR, 'test-reports')

// Test scenarios
const SCENARIOS = {
  software: {
    name: 'Software Venture (TaskMaster Pro)',
    answers: softwareVentureAnswers,
    description: 'Mobile productivity app with contribution-based revenue sharing'
  },
  creative: {
    name: 'Creative Venture (Indie Music Collective)',
    answers: creativeVentureAnswers,
    description: 'Collaborative music project with shared IP ownership'
  },
  business: {
    name: 'Business Venture (Digital Marketing Solutions)',
    answers: businessVentureAnswers,
    description: 'Consulting service with client-focused milestones'
  },
  startup: {
    name: 'Startup Venture (EcoTech Innovations)',
    answers: startupVentureAnswers,
    description: 'AI-powered sustainability platform with investment model'
  },
  all: {
    name: 'All Scenarios',
    answers: null,
    description: 'Run all venture scenarios and generate complete test suite'
  }
}

/**
 * Ensure output directories exist
 */
async function ensureDirectories() {
  const dirs = [
    OUTPUT_DIR,
    AGREEMENTS_DIR,
    PROJECT_DATA_DIR,
    REPORTS_DIR,
    path.join(AGREEMENTS_DIR, 'software'),
    path.join(AGREEMENTS_DIR, 'creative'),
    path.join(AGREEMENTS_DIR, 'business'),
    path.join(AGREEMENTS_DIR, 'startup'),
    path.join(PROJECT_DATA_DIR, 'software'),
    path.join(PROJECT_DATA_DIR, 'creative'),
    path.join(PROJECT_DATA_DIR, 'business'),
    path.join(PROJECT_DATA_DIR, 'startup')
  ]

  for (const dir of dirs) {
    try {
      await fs.mkdir(dir, { recursive: true })
    } catch (error) {
      if (error.code !== 'EEXIST') {
        console.error(`Failed to create directory ${dir}:`, error.message)
      }
    }
  }
}

/**
 * Generate agreement document from project data
 */
function generateAgreementDocument(projectData, answers) {
  const timestamp = new Date().toISOString().split('T')[0]
  
  return `# ${projectData.name} - Collaboration Agreement

**Generated:** ${timestamp}
**Agreement Type:** ${projectData.agreement_type}
**Project Type:** ${projectData.project_type}

## 📋 Project Overview

**Name:** ${projectData.name}
**Description:** ${projectData.description}
**Duration:** ${projectData.estimated_duration} months
**Target Audience:** ${projectData.target_audience}

## 💰 Financial Structure

### Revenue Model
- **Type:** ${projectData.royalty_model.model_type}
- **Base Percentage:** ${projectData.royalty_model.base_percentage}%
- **Contribution Multiplier:** ${projectData.royalty_model.contribution_multiplier}
- **Minimum Threshold:** $${projectData.royalty_model.minimum_threshold}
- **Calculation Method:** ${projectData.royalty_model.calculation_method}

### Revenue Tranches
${projectData.revenue_tranches.map(tranche => 
  `- **${tranche.name}**: $${tranche.start_amount}${tranche.end_amount ? ` - $${tranche.end_amount}` : '+'} (${tranche.percentage}%)`
).join('\n')}

## 🎯 Project Milestones

${projectData.milestones.map((milestone, index) => 
  `### ${index + 1}. ${milestone.name}
**Target Date:** ${milestone.target_date.toDateString()}
**Progress:** ${milestone.percentage}%
**Required:** ${milestone.is_required ? 'Yes' : 'No'}
**Description:** ${milestone.description}
`).join('\n')}

## 🤝 Contribution Tracking

**Categories:**
${projectData.contribution_tracking.categories.map(cat => `- ${cat}`).join('\n')}

**Tracking Method:** ${projectData.contribution_tracking.tracking_method}
**Weight System:** ${projectData.contribution_tracking.weight_system}

## ⚖️ Legal Terms

**IP Ownership:** ${projectData.ip_ownership}
**Dispute Resolution:** ${projectData.dispute_resolution}
**Team Size Limit:** ${projectData.max_team_size} members
**Approval Required:** ${projectData.requires_approval ? 'Yes' : 'No'}

## 📊 Project Configuration

**Tags:** ${projectData.tags.join(', ')}
**Icon:** ${projectData.icon}
**Public:** ${projectData.is_public ? 'Yes' : 'No'}
**Notification Frequency:** ${projectData.notification_frequency}

---

*This agreement was generated from venture setup answers and represents the collaborative framework for this project.*`
}

/**
 * Run a single test scenario
 */
async function runScenario(scenarioKey) {
  const scenario = SCENARIOS[scenarioKey]
  if (!scenario) {
    throw new Error(`Unknown scenario: ${scenarioKey}`)
  }

  console.log(`\n🧪 Running scenario: ${scenario.name}`)
  console.log(`📝 ${scenario.description}`)

  // Generate project data
  const projectData = mapVentureAnswersToProjectData(scenario.answers)
  
  // Generate agreement document
  const agreementDoc = generateAgreementDocument(projectData, scenario.answers)
  
  // Create file paths
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0]
  const agreementPath = path.join(AGREEMENTS_DIR, scenarioKey, `${projectData.name.replace(/\s+/g, '-')}-${timestamp}.md`)
  const projectDataPath = path.join(PROJECT_DATA_DIR, scenarioKey, `${projectData.name.replace(/\s+/g, '-')}-${timestamp}.json`)
  const reportPath = path.join(REPORTS_DIR, `${scenarioKey}-${timestamp}.json`)
  
  // Save files
  await fs.writeFile(agreementPath, agreementDoc, 'utf8')
  await fs.writeFile(projectDataPath, JSON.stringify(projectData, null, 2), 'utf8')
  
  // Generate test report
  const report = {
    scenario: scenarioKey,
    timestamp: new Date().toISOString(),
    projectName: projectData.name,
    agreementType: projectData.agreement_type,
    projectType: projectData.project_type,
    files: {
      agreement: agreementPath,
      projectData: projectDataPath
    },
    validation: {
      hasAllRequiredFields: !!(projectData.name && projectData.description && projectData.royalty_model),
      milestoneCount: projectData.milestones.length,
      contributionCategories: projectData.contribution_tracking.categories.length,
      revenueTranches: projectData.revenue_tranches.length
    }
  }
  
  await fs.writeFile(reportPath, JSON.stringify(report, null, 2), 'utf8')
  
  console.log(`✅ Generated files:`)
  console.log(`   📄 Agreement: ${agreementPath}`)
  console.log(`   📊 Project Data: ${projectDataPath}`)
  console.log(`   📋 Report: ${reportPath}`)
  
  return report
}

/**
 * Main execution function
 */
async function main() {
  const scenario = process.argv[2] || 'software'
  
  console.log('🚀 Venture Creation Test Runner')
  console.log('================================')
  
  try {
    await ensureDirectories()
    
    if (scenario === 'all') {
      console.log('\n🔄 Running all scenarios...')
      const reports = []
      
      for (const [key, config] of Object.entries(SCENARIOS)) {
        if (key !== 'all') {
          const report = await runScenario(key)
          reports.push(report)
        }
      }
      
      // Generate summary report
      const summaryPath = path.join(REPORTS_DIR, `summary-${new Date().toISOString().split('T')[0]}.json`)
      await fs.writeFile(summaryPath, JSON.stringify(reports, null, 2), 'utf8')
      
      console.log(`\n📊 Summary report: ${summaryPath}`)
      console.log(`✅ Generated ${reports.length} scenarios`)
      
    } else {
      await runScenario(scenario)
    }
    
    console.log('\n🎉 Test run completed successfully!')
    console.log('\n📁 Check the output directory for generated files:')
    console.log(`   ${OUTPUT_DIR}`)
    
  } catch (error) {
    console.error('❌ Test run failed:', error.message)
    process.exit(1)
  }
}

// Show available scenarios if no args
if (process.argv.length === 2) {
  console.log('🚀 Venture Creation Test Runner')
  console.log('================================')
  console.log('\nAvailable scenarios:')
  Object.entries(SCENARIOS).forEach(([key, scenario]) => {
    console.log(`  ${key.padEnd(10)} - ${scenario.description}`)
  })
  console.log('\nUsage: node interactive-test-runner.js [scenario]')
  console.log('Example: node interactive-test-runner.js software')
} else {
  main()
}
