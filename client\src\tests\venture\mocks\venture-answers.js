// Mock venture answers for different scenarios

export const softwareVentureAnswers = {
  // Question 1: What's your venture?
  ventureName: 'TaskMaster Pro',
  projectCategory: 'software',
  ventureDescription: 'A mobile app that helps teams manage tasks and track productivity with smart automation',

  // Question 2: How will you work together?
  timeline: 'medium',
  targetAudience: 'business',
  initialTeamRoles: 'standard',

  // Question 3: How will you fund this?
  budget: 'bootstrapped',
  revenueSharing: 'contribution',

  // Question 4: What are your goals?
  successMetrics: 'revenue',
  ventureTags: ['mobile', 'productivity', 'automation'],
  ventureIcon: '📱',

  // Question 5: Legal & Protection
  agreementType: 'business',
  ipOwnership: 'shared',
  disputeResolution: 'mediation'
}

export const creativeVentureAnswers = {
  ventureName: 'Indie Music Collective',
  projectCategory: 'creative',
  ventureDescription: 'A collaborative music project bringing together independent artists to create and distribute original music',
  timeline: 'long',
  targetAudience: 'general',
  initialTeamRoles: 'creative',
  budget: 'sweat_equity',
  revenueSharing: 'equal',
  successMetrics: 'satisfaction',
  ventureTags: ['music', 'creative', 'indie'],
  ventureIcon: '🎵',
  agreementType: 'simple',
  ipOwnership: 'shared',
  disputeResolution: 'discussion'
}

export const businessVentureAnswers = {
  ventureName: 'Digital Marketing Solutions',
  projectCategory: 'business',
  ventureDescription: 'A consulting service helping small businesses establish their digital presence and marketing strategy',
  timeline: 'short',
  targetAudience: 'business',
  initialTeamRoles: 'business',
  budget: 'funded',
  revenueSharing: 'investment',
  successMetrics: 'revenue',
  ventureTags: ['consulting', 'marketing', 'digital'],
  ventureIcon: '💼',
  agreementType: 'business',
  ipOwnership: 'company',
  disputeResolution: 'arbitration'
}

export const startupVentureAnswers = {
  ventureName: 'EcoTech Innovations',
  projectCategory: 'software',
  ventureDescription: 'A platform connecting eco-conscious consumers with sustainable product alternatives using AI recommendations',
  timeline: 'long',
  targetAudience: 'general',
  initialTeamRoles: 'startup',
  budget: 'revenue_first',
  revenueSharing: 'custom',
  successMetrics: 'users',
  ventureTags: ['sustainability', 'ai', 'ecommerce', 'startup'],
  ventureIcon: '🌱',
  agreementType: 'startup',
  ipOwnership: 'lead',
  disputeResolution: 'mediation'
}

// Incomplete answers for validation testing
export const incompleteAnswers = {
  ventureName: 'Test Venture',
  projectCategory: 'software',
  // Missing required fields: ventureDescription, timeline, etc.
}

// Invalid answers for error testing
export const invalidAnswers = {
  ventureName: '', // Empty required field
  projectCategory: 'invalid_category',
  ventureDescription: 'x'.repeat(5000), // Too long
  timeline: 'invalid_timeline',
  budget: null,
  revenueSharing: undefined
}

// Edge case answers
export const edgeCaseAnswers = {
  ventureName: 'A'.repeat(100), // Very long name
  projectCategory: 'software',
  ventureDescription: 'Minimal description',
  timeline: 'quick',
  targetAudience: 'internal',
  budget: 'sweat_equity',
  revenueSharing: 'equal',
  successMetrics: 'features',
  ventureTags: [], // No tags
  ventureIcon: '🔧',
  agreementType: 'simple',
  ipOwnership: 'shared',
  disputeResolution: 'discussion'
}

// All possible answer combinations for comprehensive testing
export const allAnswerCombinations = [
  softwareVentureAnswers,
  creativeVentureAnswers,
  businessVentureAnswers,
  startupVentureAnswers
]

// Helper to get answers by venture type
export const getAnswersByType = (type) => {
  const answerMap = {
    software: softwareVentureAnswers,
    creative: creativeVentureAnswers,
    business: businessVentureAnswers,
    startup: startupVentureAnswers
  }
  return answerMap[type] || softwareVentureAnswers
}

// Helper to create partial answers for testing progression
export const createPartialAnswers = (questionNumber) => {
  const base = softwareVentureAnswers
  const questions = [
    ['ventureName', 'projectCategory', 'ventureDescription'],
    ['timeline', 'targetAudience', 'initialTeamRoles'],
    ['budget', 'revenueSharing'],
    ['successMetrics', 'ventureTags', 'ventureIcon'],
    ['agreementType', 'ipOwnership', 'disputeResolution']
  ]
  
  const fieldsToInclude = questions.slice(0, questionNumber).flat()
  const partialAnswers = {}
  
  fieldsToInclude.forEach(field => {
    if (base[field] !== undefined) {
      partialAnswers[field] = base[field]
    }
  })
  
  return partialAnswers
}
