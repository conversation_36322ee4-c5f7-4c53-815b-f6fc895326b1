# Venture Testing Output Directory

This directory contains generated agreements and project data from venture creation tests.

## 📁 Directory Structure

```
output/
├── agreements/           # Generated agreement documents
│   ├── software/        # Software venture agreements
│   ├── creative/        # Creative venture agreements
│   ├── business/        # Business venture agreements
│   └── startup/         # Startup venture agreements
├── project-data/        # Generated project data structures
│   ├── software/        # Software project data
│   ├── creative/        # Creative project data
│   ├── business/        # Business project data
│   └── startup/         # Startup project data
├── test-reports/        # Test execution reports
└── comparisons/         # Before/after comparisons

```

## 🧪 Test Scenarios

### Software Ventures
- **TaskMaster Pro**: Mobile productivity app with contribution-based revenue sharing
- **Developer Tool**: Open source tool with equal split model
- **SaaS Platform**: Subscription service with investment-based model

### Creative Ventures
- **Indie Music Collective**: Collaborative music project with shared IP
- **Art Installation**: Physical art project with bootstrapped funding
- **Documentary Film**: Creative documentary with revenue-first approach

### Business Ventures
- **Digital Marketing Solutions**: Consulting service with client-focused milestones
- **Professional Services**: B2B service with funded approach
- **Coaching Platform**: Educational service with custom revenue model

### Startup Ventures
- **EcoTech Innovations**: AI-powered sustainability platform
- **FinTech Solution**: Financial technology startup
- **HealthTech Platform**: Healthcare innovation venture

## 📊 Generated Files

Each test scenario generates:
- **Agreement Document** (`.md` format): Complete legal agreement
- **Project Data** (`.json` format): Structured project configuration
- **Test Report** (`.json` format): Validation results and metrics
- **Summary** (`.txt` format): Human-readable test summary

## 🔄 Regeneration

To regenerate all test outputs:
```bash
npm run test:venture:generate
```

To run specific scenario:
```bash
npm run test:venture:scenario -- software
```

## 📝 Review Process

1. **Automated Validation**: Tests verify data structure and completeness
2. **Manual Review**: Human review of generated agreements for accuracy
3. **Comparison Testing**: Before/after comparisons for iterative improvements
4. **Edge Case Testing**: Validation of error handling and edge cases

## 🎯 Quality Metrics

- **Agreement Completeness**: All required sections present
- **Data Consistency**: Project data matches venture answers
- **Legal Accuracy**: Terms properly reflect user selections
- **User Experience**: Flow feels cohesive and logical
