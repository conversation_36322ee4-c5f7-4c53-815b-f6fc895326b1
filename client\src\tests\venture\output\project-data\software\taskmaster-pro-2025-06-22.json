{"name": "TaskMaster Pro", "title": "TaskMaster Pro", "description": "A mobile app that helps teams manage tasks and track productivity with smart automation", "project_type": "software", "estimated_duration": 6, "start_date": "2025-06-22T00:02:08.386Z", "target_audience": "business", "is_public": true, "icon": "📱", "tags": ["mobile", "productivity", "automation"], "royalty_model": {"model_type": "contribution_based", "base_percentage": 0, "contribution_multiplier": 1, "minimum_threshold": 0, "calculation_method": "dynamic"}, "revenue_tranches": [{"name": "Initial Revenue", "start_amount": 0, "end_amount": 1000, "percentage": 100, "description": "First revenue milestone"}, {"name": "Growth Phase", "start_amount": 1000, "end_amount": 10000, "percentage": 80, "description": "Revenue growth phase"}, {"name": "Scale Phase", "start_amount": 10000, "end_amount": null, "percentage": 60, "description": "Scaling revenue phase"}], "contribution_tracking": {"categories": ["Development", "Design", "Testing", "Documentation", "DevOps"], "tracking_method": "time_and_impact", "weight_system": "balanced"}, "milestones": [{"name": "MVP Development", "description": "Complete minimum viable product", "target_date": "2025-08-06T00:02:08.386Z", "percentage": 25, "is_required": true}, {"name": "Beta Testing", "description": "User testing and feedback collection", "target_date": "2025-09-20T00:02:08.386Z", "percentage": 50, "is_required": false}, {"name": "Launch", "description": "Public release and marketing", "target_date": "2025-11-04T00:02:08.386Z", "percentage": 75, "is_required": true}, {"name": "User Growth", "description": "Achieve user acquisition targets", "target_date": "2025-12-19T00:02:08.386Z", "percentage": 100, "is_required": false}], "agreement_type": "business", "ip_ownership": "shared", "dispute_resolution": "mediation", "max_team_size": 8, "requires_approval": false, "notification_frequency": "weekly", "contribution_categories": ["Development", "Design", "Testing", "Documentation", "DevOps"], "milestone_templates": [{"name": "MVP Development", "description": "Complete minimum viable product", "required": true}, {"name": "Beta Testing", "description": "User testing and feedback collection"}, {"name": "Launch", "description": "Public release and marketing", "required": true}, {"name": "User Growth", "description": "Achieve user acquisition targets"}, {"name": "Revenue Generation", "description": "First revenue milestone", "required": true}, {"name": "Scale Operations", "description": "Expand and optimize", "required": true}]}