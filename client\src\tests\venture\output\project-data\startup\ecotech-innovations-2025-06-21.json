{"name": "EcoTech Innovations", "title": "EcoTech Innovations", "description": "A platform connecting eco-conscious consumers with sustainable product alternatives using AI recommendations", "project_type": "software", "estimated_duration": 12, "start_date": "2025-06-21T20:27:12.498Z", "target_audience": "general", "is_public": true, "icon": "🌱", "tags": ["sustainability", "ai", "ecommerce", "startup"], "royalty_model": {"model_type": "custom", "base_percentage": 0, "contribution_multiplier": 1, "minimum_threshold": 0, "calculation_method": "manual"}, "revenue_tranches": [{"name": "Initial Revenue", "start_amount": 0, "end_amount": 1000, "percentage": 100, "description": "First revenue milestone"}, {"name": "Growth Phase", "start_amount": 1000, "end_amount": 10000, "percentage": 80, "description": "Revenue growth phase"}, {"name": "Scale Phase", "start_amount": 10000, "end_amount": null, "percentage": 60, "description": "Scaling revenue phase"}], "contribution_tracking": {"categories": ["Development", "Design", "Testing", "Documentation", "DevOps"], "tracking_method": "time_and_impact", "weight_system": "balanced"}, "milestones": [{"name": "MVP Development", "description": "Complete minimum viable product", "target_date": "2025-08-20T20:27:12.498Z", "percentage": 17, "is_required": true}, {"name": "Beta Testing", "description": "User testing and feedback collection", "target_date": "2025-10-19T20:27:12.498Z", "percentage": 33, "is_required": false}, {"name": "Launch", "description": "Public release and marketing", "target_date": "2025-12-18T20:27:12.498Z", "percentage": 50, "is_required": true}, {"name": "User Growth", "description": "Achieve user acquisition targets", "target_date": "2026-02-16T20:27:12.498Z", "percentage": 67, "is_required": true}, {"name": "Revenue Generation", "description": "First revenue milestone", "target_date": "2026-04-17T20:27:12.498Z", "percentage": 83, "is_required": false}, {"name": "Scale Operations", "description": "Expand and optimize", "target_date": "2026-06-16T20:27:12.498Z", "percentage": 100, "is_required": false}], "agreement_type": "startup", "ip_ownership": "lead", "dispute_resolution": "mediation", "max_team_size": 8, "requires_approval": false, "notification_frequency": "weekly", "contribution_categories": ["Development", "Design", "Testing", "Documentation", "DevOps"], "milestone_templates": [{"name": "MVP Development", "description": "Complete minimum viable product", "required": true}, {"name": "Beta Testing", "description": "User testing and feedback collection"}, {"name": "Launch", "description": "Public release and marketing", "required": true}, {"name": "User Growth", "description": "Achieve user acquisition targets", "required": true}, {"name": "Revenue Generation", "description": "First revenue milestone"}, {"name": "Scale Operations", "description": "Expand and optimize"}]}