#!/usr/bin/env node

/**
 * Run All Venture Scenarios and Generate Comparison Report
 * 
 * This script runs all venture scenarios and creates a comprehensive
 * comparison report showing the differences between venture types.
 */

import fs from 'fs/promises'
import path from 'path'

import { 
  softwareVentureAnswers, 
  creativeVentureAnswers, 
  businessVentureAnswers,
  startupVentureAnswers 
} from './mocks/venture-answers.js'

import { mapVentureAnswersToProjectData } from '../../utils/venture-mapping.js'

const OUTPUT_DIR = path.resolve(process.cwd(), 'src/tests/venture/output')

const scenarios = [
  { name: 'software', answers: softwareVentureAnswers, description: 'Mobile productivity app' },
  { name: 'creative', answers: creativeVentureAnswers, description: 'Music collaboration project' },
  { name: 'business', answers: businessVentureAnswers, description: 'Digital marketing consulting' },
  { name: 'startup', answers: startupVentureAnswers, description: 'AI sustainability platform' }
]

/**
 * Generate comparison report
 */
function generateComparisonReport(results) {
  const timestamp = new Date().toISOString()
  
  return `# Venture Creation Pipeline - Comparison Report

**Generated:** ${timestamp}
**Scenarios Tested:** ${results.length}

## 📊 Overview

${results.map(result => `
### ${result.name.toUpperCase()}: ${result.projectData.name}
- **Type:** ${result.projectData.project_type}
- **Duration:** ${result.projectData.estimated_duration} months
- **Revenue Model:** ${result.projectData.royalty_model.model_type}
- **Team Size:** ${result.projectData.max_team_size} members
- **Milestones:** ${result.projectData.milestones.length}
- **Contribution Categories:** ${result.projectData.contribution_tracking.categories.length}
`).join('\n')}

## 💰 Revenue Model Comparison

| Scenario | Model Type | Base % | Multiplier | Threshold | Method |
|----------|------------|--------|------------|-----------|---------|
${results.map(r => 
  `| ${r.name} | ${r.projectData.royalty_model.model_type} | ${r.projectData.royalty_model.base_percentage}% | ${r.projectData.royalty_model.contribution_multiplier}x | $${r.projectData.royalty_model.minimum_threshold} | ${r.projectData.royalty_model.calculation_method} |`
).join('\n')}

## 🎯 Milestone Comparison

${results.map(result => `
### ${result.name.toUpperCase()} Milestones
${result.projectData.milestones.map((m, i) => 
  `${i + 1}. **${m.name}** (${m.percentage}%) - ${m.is_required ? 'Required' : 'Optional'}`
).join('\n')}
`).join('\n')}

## 🤝 Contribution Categories

${results.map(result => `
### ${result.name.toUpperCase()}
${result.projectData.contribution_tracking.categories.map(cat => `- ${cat}`).join('\n')}
`).join('\n')}

## ⚖️ Legal Framework Comparison

| Scenario | Agreement Type | IP Ownership | Dispute Resolution | Approval Required |
|----------|----------------|--------------|-------------------|-------------------|
${results.map(r => 
  `| ${r.name} | ${r.projectData.agreement_type} | ${r.projectData.ip_ownership} | ${r.projectData.dispute_resolution} | ${r.projectData.requires_approval ? 'Yes' : 'No'} |`
).join('\n')}

## 📈 Revenue Tranches Analysis

${results.map(result => `
### ${result.name.toUpperCase()} Revenue Structure
${result.projectData.revenue_tranches.map((tranche, i) => 
  `${i + 1}. **${tranche.name}**: $${tranche.start_amount}${tranche.end_amount ? ` - $${tranche.end_amount}` : '+'} (${tranche.percentage}%)`
).join('\n')}
`).join('\n')}

## 🔍 Key Insights

### Revenue Model Distribution
- **Contribution-based:** ${results.filter(r => r.projectData.royalty_model.model_type === 'contribution_based').length} scenarios
- **Equal Split:** ${results.filter(r => r.projectData.royalty_model.model_type === 'equal_split').length} scenarios
- **Investment-based:** ${results.filter(r => r.projectData.royalty_model.model_type === 'investment_based').length} scenarios
- **Custom:** ${results.filter(r => r.projectData.royalty_model.model_type === 'custom').length} scenarios

### Timeline Distribution
- **Quick (1 month):** ${results.filter(r => r.projectData.estimated_duration === 1).length} scenarios
- **Short (2 months):** ${results.filter(r => r.projectData.estimated_duration === 2).length} scenarios
- **Medium (6 months):** ${results.filter(r => r.projectData.estimated_duration === 6).length} scenarios
- **Long (12 months):** ${results.filter(r => r.projectData.estimated_duration === 12).length} scenarios

### Team Size Analysis
- **Average Team Size:** ${Math.round(results.reduce((sum, r) => sum + r.projectData.max_team_size, 0) / results.length)} members
- **Range:** ${Math.min(...results.map(r => r.projectData.max_team_size))} - ${Math.max(...results.map(r => r.projectData.max_team_size))} members

## ✅ Validation Results

${results.map(result => `
### ${result.name.toUpperCase()} Validation
- **Required Fields:** ✅ Complete
- **Milestones:** ✅ ${result.projectData.milestones.length} generated
- **Contribution Categories:** ✅ ${result.projectData.contribution_tracking.categories.length} categories
- **Revenue Tranches:** ✅ ${result.projectData.revenue_tranches.length} tranches
- **Legal Framework:** ✅ Complete
`).join('\n')}

## 📁 Generated Files

${results.map(result => `
### ${result.name.toUpperCase()}
- Agreement: \`output/agreements/${result.name}/${result.fileName}.md\`
- Project Data: \`output/project-data/${result.name}/${result.fileName}.json\`
- Answers: \`output/project-data/${result.name}/${result.fileName}-answers.json\`
`).join('\n')}

---

*This report was automatically generated by the Royaltea venture creation testing pipeline.*`
}

/**
 * Main execution
 */
async function main() {
  console.log('🚀 Running All Venture Scenarios')
  console.log('=================================')
  
  const results = []
  
  for (const scenario of scenarios) {
    console.log(`\n🧪 Processing ${scenario.name}: ${scenario.description}`)
    
    // Generate project data
    const projectData = mapVentureAnswersToProjectData(scenario.answers)
    
    // Create safe filename
    const timestamp = new Date().toISOString().split('T')[0]
    const safeName = projectData.name.replace(/[^a-zA-Z0-9]/g, '-').toLowerCase()
    const fileName = `${safeName}-${timestamp}`
    
    // Store result
    results.push({
      name: scenario.name,
      description: scenario.description,
      projectData,
      answers: scenario.answers,
      fileName
    })
    
    console.log(`   ✅ ${projectData.name} (${projectData.project_type})`)
    console.log(`   💰 ${projectData.royalty_model.model_type} revenue model`)
    console.log(`   🎯 ${projectData.milestones.length} milestones`)
  }
  
  // Generate comparison report
  const report = generateComparisonReport(results)
  const reportPath = path.join(OUTPUT_DIR, `comparison-report-${new Date().toISOString().split('T')[0]}.md`)
  
  await fs.writeFile(reportPath, report, 'utf8')
  
  console.log('\n📊 Comparison Report Generated')
  console.log(`   📄 ${reportPath}`)
  
  console.log('\n🎉 All scenarios completed successfully!')
  console.log(`\n📁 Check the output directory: ${OUTPUT_DIR}`)
  
  // Summary
  console.log('\n📈 Summary:')
  console.log(`   • ${results.length} scenarios processed`)
  console.log(`   • ${results.reduce((sum, r) => sum + r.projectData.milestones.length, 0)} total milestones generated`)
  console.log(`   • ${new Set(results.flatMap(r => r.projectData.contribution_tracking.categories)).size} unique contribution categories`)
  console.log(`   • ${new Set(results.map(r => r.projectData.royalty_model.model_type)).size} different revenue models`)
}

main().catch(console.error)
