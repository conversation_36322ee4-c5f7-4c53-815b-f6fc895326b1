#!/usr/bin/env node

/**
 * Venture Creation Unit Test Runner
 * 
 * Runs all unit tests for the venture creation system and generates
 * a comprehensive report of test results and coverage.
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const COLORS = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorize(text, color) {
  return `${COLORS[color]}${text}${COLORS.reset}`;
}

function printHeader(title) {
  const border = '='.repeat(60);
  console.log(colorize(border, 'cyan'));
  console.log(colorize(`  ${title}`, 'bright'));
  console.log(colorize(border, 'cyan'));
  console.log();
}

function printSection(title) {
  console.log(colorize(`\n📋 ${title}`, 'blue'));
  console.log(colorize('-'.repeat(40), 'blue'));
}

async function runTests() {
  printHeader('VENTURE CREATION UNIT TESTS');
  
  const testFiles = [
    'src/tests/venture/unit/venture-mapping.test.js',
    'src/tests/venture/unit/legal-agreement-generator.test.js',
    'src/tests/venture/unit/venture-question-flow.test.js'
  ];
  
  const results = {
    total: 0,
    passed: 0,
    failed: 0,
    skipped: 0,
    duration: 0,
    coverage: null,
    details: []
  };
  
  console.log(colorize('🧪 Running unit tests for venture creation system...', 'bright'));
  console.log();
  
  try {
    // Run tests with coverage
    printSection('Test Execution');
    
    const startTime = Date.now();
    
    const testCommand = [
      'npx vitest run',
      '--coverage',
      '--reporter=verbose',
      '--reporter=json',
      '--outputFile=test-results.json',
      testFiles.join(' ')
    ].join(' ');
    
    console.log(colorize(`Command: ${testCommand}`, 'yellow'));
    console.log();
    
    try {
      const output = execSync(testCommand, { 
        encoding: 'utf8',
        stdio: 'pipe',
        cwd: process.cwd()
      });
      
      console.log(output);
      
    } catch (error) {
      // Vitest might exit with non-zero even on partial success
      console.log(error.stdout || error.message);
      if (error.stderr) {
        console.error(colorize('Stderr:', 'red'), error.stderr);
      }
    }
    
    const endTime = Date.now();
    results.duration = endTime - startTime;
    
    // Parse results if available
    try {
      if (fs.existsSync('test-results.json')) {
        const testResults = JSON.parse(fs.readFileSync('test-results.json', 'utf8'));
        
        if (testResults.testResults) {
          testResults.testResults.forEach(file => {
            const fileResult = {
              file: file.name,
              tests: file.assertionResults.length,
              passed: file.assertionResults.filter(t => t.status === 'passed').length,
              failed: file.assertionResults.filter(t => t.status === 'failed').length,
              skipped: file.assertionResults.filter(t => t.status === 'skipped').length
            };
            
            results.details.push(fileResult);
            results.total += fileResult.tests;
            results.passed += fileResult.passed;
            results.failed += fileResult.failed;
            results.skipped += fileResult.skipped;
          });
        }
      }
    } catch (parseError) {
      console.log(colorize('⚠️ Could not parse detailed test results', 'yellow'));
    }
    
    // Generate summary report
    printSection('Test Results Summary');
    
    console.log(colorize(`📊 Total Tests: ${results.total}`, 'bright'));
    console.log(colorize(`✅ Passed: ${results.passed}`, 'green'));
    console.log(colorize(`❌ Failed: ${results.failed}`, results.failed > 0 ? 'red' : 'green'));
    console.log(colorize(`⏭️ Skipped: ${results.skipped}`, 'yellow'));
    console.log(colorize(`⏱️ Duration: ${results.duration}ms`, 'cyan'));
    
    if (results.details.length > 0) {
      printSection('Detailed Results by File');
      
      results.details.forEach(detail => {
        const fileName = path.basename(detail.file);
        const status = detail.failed > 0 ? 'red' : 'green';
        const statusIcon = detail.failed > 0 ? '❌' : '✅';
        
        console.log(colorize(`${statusIcon} ${fileName}`, status));
        console.log(`   Tests: ${detail.tests}, Passed: ${detail.passed}, Failed: ${detail.failed}`);
      });
    }
    
    // Test specific functionality
    printSection('Functionality Verification');
    
    console.log('🔍 Verifying core functions...');
    
    try {
      // Test venture mapping
      const { mapVentureAnswersToProjectData } = await import('../../utils/venture-mapping.js');
      const testAnswers = {
        ventureName: 'Unit Test Venture',
        projectCategory: 'software',
        timeline: 'medium'
      };
      
      const mappedData = mapVentureAnswersToProjectData(testAnswers);
      if (mappedData && mappedData.name === 'Unit Test Venture') {
        console.log(colorize('✅ Venture mapping function working', 'green'));
      } else {
        console.log(colorize('❌ Venture mapping function failed', 'red'));
      }
      
    } catch (error) {
      console.log(colorize(`❌ Venture mapping test failed: ${error.message}`, 'red'));
    }
    
    try {
      // Test legal agreement generation
      const { generateLegalAgreement } = await import('../../utils/legal-agreement-generator.js');
      const testProject = {
        name: 'Test Project',
        description: 'Test description',
        project_type: 'software'
      };
      const testAnswers = { ventureName: 'Test' };
      const testContributor = { name: 'Test Contributor' };
      
      const agreement = generateLegalAgreement(testProject, testAnswers, testContributor);
      if (agreement && agreement.includes('CITY OF GAMERS INC.')) {
        console.log(colorize('✅ Legal agreement generator working', 'green'));
      } else {
        console.log(colorize('❌ Legal agreement generator failed', 'red'));
      }
      
    } catch (error) {
      console.log(colorize(`❌ Legal agreement test failed: ${error.message}`, 'red'));
    }
    
    // Generate test report
    printSection('Test Report Generation');
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: results,
      environment: {
        node: process.version,
        platform: process.platform,
        cwd: process.cwd()
      },
      testFiles: testFiles,
      status: results.failed === 0 ? 'PASSED' : 'FAILED'
    };
    
    const reportPath = 'src/tests/venture/unit-test-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(colorize(`📄 Test report saved to: ${reportPath}`, 'cyan'));
    
    // Final status
    printSection('Final Status');
    
    if (results.failed === 0) {
      console.log(colorize('🎉 ALL TESTS PASSED!', 'green'));
      console.log(colorize('✅ Venture creation system is ready for integration testing', 'green'));
    } else {
      console.log(colorize('⚠️ SOME TESTS FAILED', 'red'));
      console.log(colorize('❌ Please fix failing tests before proceeding', 'red'));
    }
    
    // Cleanup
    if (fs.existsSync('test-results.json')) {
      fs.unlinkSync('test-results.json');
    }
    
    return results.failed === 0;
    
  } catch (error) {
    console.error(colorize('💥 Test execution failed:', 'red'), error.message);
    return false;
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error(colorize('💥 Fatal error:', 'red'), error);
    process.exit(1);
  });
}

export { runTests };
