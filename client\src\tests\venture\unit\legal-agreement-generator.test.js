import { describe, it, expect, beforeEach } from 'vitest';
import { generateLegalAgreement } from '../../../utils/legal-agreement-generator.js';

/**
 * Unit Tests for Legal Agreement Generator
 * 
 * Tests the core logic that generates lawyer-approved legal agreements
 * from project data and venture answers.
 */

describe('Legal Agreement Generator', () => {
  let mockProjectData;
  let mockAnswers;
  let mockVentureOwnerInfo;
  let mockContributorInfo;

  beforeEach(() => {
    mockProjectData = {
      name: 'Test Venture Pro',
      description: 'A comprehensive test venture for legal agreement generation',
      project_type: 'software',
      estimated_duration: 6,
      start_date: new Date('2025-06-21'),
      target_audience: 'business',
      max_team_size: 8,
      tags: ['productivity', 'automation', 'mobile'],
      royalty_model: {
        model_type: 'contribution_based',
        base_percentage: 0,
        contribution_multiplier: 1,
        minimum_threshold: 0,
        calculation_method: 'dynamic'
      },
      revenue_tranches: [
        {
          name: 'Initial Revenue',
          start_amount: 0,
          end_amount: 1000,
          percentage: 100,
          description: 'First revenue milestone'
        },
        {
          name: 'Growth Phase',
          start_amount: 1000,
          end_amount: 10000,
          percentage: 80,
          description: 'Revenue growth phase'
        }
      ],
      milestones: [
        {
          name: 'MVP Development',
          target_date: new Date('2025-08-05'),
          percentage: 25,
          is_required: true,
          description: 'Complete minimum viable product'
        }
      ],
      contribution_tracking: {
        categories: ['Development', 'Design', 'Testing', 'Documentation', 'DevOps'],
        tracking_method: 'time_and_impact',
        weight_system: 'balanced'
      },
      ip_ownership: 'shared',
      dispute_resolution: 'mediation',
      agreement_type: 'business',
      notification_frequency: 'weekly'
    };

    mockAnswers = {
      ventureName: 'Test Venture Pro',
      projectCategory: 'software',
      ventureDescription: 'A comprehensive test venture',
      timeline: 'medium',
      targetAudience: 'business',
      budget: 'bootstrapped',
      revenueSharing: 'contribution',
      successMetrics: 'revenue',
      ventureTags: ['productivity', 'automation', 'mobile'],
      agreementType: 'business',
      ipOwnership: 'shared',
      disputeResolution: 'mediation'
    };

    mockVentureOwnerInfo = {
      companyName: 'Test Venture Pro',
      ownerName: 'Test Owner',
      ownerTitle: 'Founder',
      address: '456 Venture Ave',
      address2: 'Venture City, VC 67890',
      address3: 'United States',
      state: 'Florida',
      entityType: 'LLC'
    };

    mockContributorInfo = {
      name: 'Test Contributor',
      isCompany: false,
      address: '123 Test Street',
      address2: 'Test City, TS 12345',
      address3: 'United States'
    };
  });

  describe('generateLegalAgreement', () => {
    it('should generate a complete legal agreement', () => {
      const agreement = generateLegalAgreement(mockProjectData, mockAnswers, mockVentureOwnerInfo, mockContributorInfo);

      expect(agreement).toBeDefined();
      expect(typeof agreement).toBe('string');
      expect(agreement.length).toBeGreaterThan(1000); // Should be substantial
    });

    it('should include required legal header and company information', () => {
      const agreement = generateLegalAgreement(mockProjectData, mockAnswers, mockVentureOwnerInfo, mockContributorInfo);

      expect(agreement).toContain('TEST VENTURE PRO');
      expect(agreement).toContain('CONTRIBUTOR AGREEMENT');
      expect(agreement).toContain('Test Venture Pro');
      expect(agreement).toContain('456 Venture Ave');
      expect(agreement).toContain('Test Owner');
      expect(agreement).toContain('Founder');
    });

    it('should include proper recitals section', () => {
      const agreement = generateLegalAgreement(mockProjectData, mockAnswers, mockVentureOwnerInfo, mockContributorInfo);

      expect(agreement).toContain('Recitals');
      expect(agreement).toContain('WHEREAS');
      expect(agreement).toContain('NOW THEREFORE');
    });

    it('should include all required definitions', () => {
      const agreement = generateLegalAgreement(mockProjectData, mockAnswers, mockVentureOwnerInfo, mockContributorInfo);
      
      const requiredDefinitions = [
        'Background IP',
        'Confidential Documents',
        'Confidential Information',
        'Contribution',
        'Developed IP',
        'Governmental Authority',
        'Launch',
        'Milestones',
        'Person',
        'Programs',
        'Specification',
        'Termination Date',
        'Work Product',
        'Work Product Management',
        'Revenue Tranche',
        'Contribution Points'
      ];

      requiredDefinitions.forEach(definition => {
        expect(agreement).toContain(definition);
      });
    });

    it('should include Schedule A with project-specific details', () => {
      const agreement = generateLegalAgreement(mockProjectData, mockAnswers, mockVentureOwnerInfo, mockContributorInfo);

      expect(agreement).toContain('SCHEDULE A');
      expect(agreement).toContain('Description of Services');
      expect(agreement).toContain('Test Venture Pro');
      expect(agreement).toContain('software');
      expect(agreement).toContain('6 months');
      expect(agreement).toContain('business');
      expect(agreement).toContain('8 contributors');
    });

    it('should include Schedule B with revenue model details', () => {
      const agreement = generateLegalAgreement(mockProjectData, mockAnswers, mockVentureOwnerInfo, mockContributorInfo);

      expect(agreement).toContain('SCHEDULE B');
      expect(agreement).toContain('Consideration');
      expect(agreement).toContain('Revenue Sharing Model');
      expect(agreement).toContain('contribution based');
      expect(agreement).toContain('Revenue Tranches');
      expect(agreement).toContain('Initial Revenue');
      expect(agreement).toContain('Growth Phase');
      expect(agreement).toContain('Contribution Point System');
    });

    it('should include Exhibit I with technical specifications', () => {
      const agreement = generateLegalAgreement(mockProjectData, mockAnswers, mockVentureOwnerInfo, mockContributorInfo);

      expect(agreement).toContain('EXHIBIT I');
      expect(agreement).toContain('Technical Specifications');
      expect(agreement).toContain('Test Venture Pro');
      expect(agreement).toContain('Mobile (iOS/Android)');
      expect(agreement).toContain('Modern web technologies');
      expect(agreement).toContain('Productivity functionality');
      expect(agreement).toContain('Automation functionality');
    });

    it('should include Exhibit II with project roadmap', () => {
      const agreement = generateLegalAgreement(mockProjectData, mockAnswers, mockVentureOwnerInfo, mockContributorInfo);

      expect(agreement).toContain('EXHIBIT II');
      expect(agreement).toContain('Project Roadmap and Milestones');
      expect(agreement).toContain('6 months');
      expect(agreement).toContain('MVP Development');
      expect(agreement).toContain('25%');
      expect(agreement).toContain('Weekly Meetings');
    });

    it('should include proper signature blocks', () => {
      const agreement = generateLegalAgreement(mockProjectData, mockAnswers, mockVentureOwnerInfo, mockContributorInfo);

      expect(agreement).toContain('IN WITNESS WHEREOF');
      expect(agreement).toContain('COMPANY:');
      expect(agreement).toContain('Test Venture Pro');
      expect(agreement).toContain('Test Owner');
      expect(agreement).toContain('Founder');
      expect(agreement).toContain('CONTRIBUTOR:');
      expect(agreement).toContain('Test Contributor');
    });

    it('should handle individual contributor information correctly', () => {
      const agreement = generateLegalAgreement(mockProjectData, mockAnswers, mockVentureOwnerInfo, mockContributorInfo);

      expect(agreement).toContain('[If an individual]');
      expect(agreement).toContain('Test Contributor');
      expect(agreement).toContain('123 Test Street');
      expect(agreement).toContain('Test City, TS 12345');
      expect(agreement).toContain('United States');
    });

    it('should handle company contributor information correctly', () => {
      const companyContributor = {
        name: 'Test Company LLC',
        isCompany: true,
        companyName: 'Test Company LLC',
        signatory: 'John Doe',
        signatoryName: 'John Doe',
        signatoryTitle: 'CEO',
        address: '456 Business Ave',
        address2: 'Business City, BC 67890',
        address3: 'United States'
      };

      const agreement = generateLegalAgreement(mockProjectData, mockAnswers, mockVentureOwnerInfo, companyContributor);

      expect(agreement).toContain('[If a company]');
      expect(agreement).toContain('Test Company LLC');
      expect(agreement).toContain('John Doe');
      expect(agreement).toContain('CEO');
      expect(agreement).toContain('456 Business Ave');
    });

    it('should adapt content based on project type', () => {
      const creativeProject = {
        ...mockProjectData,
        project_type: 'creative',
        tags: ['design', 'art', 'creative']
      };

      const agreement = generateLegalAgreement(creativeProject, mockAnswers, mockVentureOwnerInfo, mockContributorInfo);
      
      expect(agreement).toContain('Multi-platform Distribution');
      expect(agreement).toContain('Industry-standard creative tools');
      expect(agreement).toContain('High-quality output');
    });

    it('should adapt content based on revenue model', () => {
      const equalSplitProject = {
        ...mockProjectData,
        royalty_model: {
          model_type: 'equal_split',
          base_percentage: 50,
          contribution_multiplier: 1,
          minimum_threshold: 100,
          calculation_method: 'fixed'
        }
      };

      const agreement = generateLegalAgreement(equalSplitProject, mockAnswers, mockVentureOwnerInfo, mockContributorInfo);
      
      expect(agreement).toContain('equal split');
      expect(agreement).toContain('50%');
      expect(agreement).toContain('fixed');
    });

    it('should handle missing or invalid data gracefully', () => {
      const incompleteProject = {
        name: 'Minimal Project',
        description: 'Basic description'
      };

      const incompleteAnswers = {
        ventureName: 'Minimal Project'
      };

      const incompleteContributor = {
        name: 'Basic Contributor'
      };

      expect(() => {
        generateLegalAgreement(incompleteProject, incompleteAnswers, {}, incompleteContributor);
      }).not.toThrow();

      const agreement = generateLegalAgreement(incompleteProject, incompleteAnswers, {}, incompleteContributor);
      expect(agreement).toContain('MINIMAL PROJECT');
      expect(agreement).toContain('Minimal Project');
    });

    it('should generate consistent agreements for same input', () => {
      const agreement1 = generateLegalAgreement(mockProjectData, mockAnswers, mockVentureOwnerInfo, mockContributorInfo);
      const agreement2 = generateLegalAgreement(mockProjectData, mockAnswers, mockVentureOwnerInfo, mockContributorInfo);
      
      // Should be identical except for dynamic dates
      const normalize = (text) => text.replace(/\d{4}-\d{2}-\d{2}/g, 'DATE');
      expect(normalize(agreement1)).toBe(normalize(agreement2));
    });

    it('should be substantial enough for legal use', () => {
      const agreement = generateLegalAgreement(mockProjectData, mockAnswers, mockVentureOwnerInfo, mockContributorInfo);
      
      // Should be at least 300 lines (rough estimate for legal document)
      const lineCount = agreement.split('\n').length;
      expect(lineCount).toBeGreaterThan(300);
      
      // Should contain substantial legal content
      expect(agreement.length).toBeGreaterThan(15000); // Substantial character count
    });
  });
});
