import { describe, it, expect, beforeEach } from 'vitest';
import { mapVentureAnswersToProjectData } from '../../../utils/venture-mapping.js';

/**
 * Unit Tests for Venture Mapping Functions
 * 
 * Tests the core logic that converts venture setup answers
 * into comprehensive project data structures.
 */

describe('Venture Mapping Functions', () => {
  let mockAnswers;

  beforeEach(() => {
    mockAnswers = {
      ventureName: 'Test Venture',
      projectCategory: 'software',
      projectSubtype: 'mobile',
      ventureDescription: 'A test venture for unit testing',
      timeline: 'medium',
      targetAudience: 'business',
      budget: 'bootstrapped',
      revenueSharing: 'contribution',
      successMetrics: 'revenue',
      ventureTags: ['productivity', 'automation'],
      ventureIcon: '🚀',
      agreementType: 'business',
      ipOwnership: 'shared',
      disputeResolution: 'mediation'
    };
  });

  describe('mapVentureAnswersToProjectData', () => {
    it('should map basic project information correctly', () => {
      const result = mapVentureAnswersToProjectData(mockAnswers);

      expect(result.name).toBe('Test Venture');
      expect(result.description).toBe('A test venture for unit testing');
      expect(result.project_type).toBe('software');
      expect(result.target_audience).toBe('business');
      expect(result.tags).toEqual(['productivity', 'automation']);
      expect(result.icon).toBe('🚀');
      expect(result.is_public).toBe(true);
    });

    it('should map timeline to estimated duration correctly', () => {
      const testCases = [
        { timeline: 'quick', expected: 1 },
        { timeline: 'short', expected: 2 },
        { timeline: 'medium', expected: 6 },
        { timeline: 'long', expected: 12 },
        { timeline: 'flexible', expected: 6 }
      ];

      testCases.forEach(({ timeline, expected }) => {
        const answers = { ...mockAnswers, timeline };
        const result = mapVentureAnswersToProjectData(answers);
        expect(result.estimated_duration).toBe(expected);
      });
    });

    it('should generate appropriate royalty model based on budget', () => {
      const testCases = [
        { budget: 'bootstrapped', expectedType: 'contribution_based' },
        { budget: 'funded', expectedType: 'investment_based' },
        { budget: 'sweat_equity', expectedType: 'equal_split' },
        { budget: 'revenue_first', expectedType: 'custom' }
      ];

      testCases.forEach(({ budget, expectedType }) => {
        const answers = { ...mockAnswers, budget };
        const result = mapVentureAnswersToProjectData(answers);
        expect(result.royalty_model.model_type).toBe(expectedType);
      });
    });

    it('should generate revenue tranches based on timeline and budget', () => {
      const result = mapVentureAnswersToProjectData(mockAnswers);
      
      expect(result.revenue_tranches).toBeInstanceOf(Array);
      expect(result.revenue_tranches.length).toBeGreaterThan(0);
      
      // Check first tranche structure
      const firstTranche = result.revenue_tranches[0];
      expect(firstTranche).toHaveProperty('name');
      expect(firstTranche).toHaveProperty('start_amount');
      expect(firstTranche).toHaveProperty('percentage');
      expect(firstTranche).toHaveProperty('description');
    });

    it('should generate milestones based on timeline', () => {
      const result = mapVentureAnswersToProjectData(mockAnswers);
      
      expect(result.milestones).toBeInstanceOf(Array);
      expect(result.milestones.length).toBeGreaterThan(0);
      
      // Check milestone structure
      const firstMilestone = result.milestones[0];
      expect(firstMilestone).toHaveProperty('name');
      expect(firstMilestone).toHaveProperty('target_date');
      expect(firstMilestone).toHaveProperty('percentage');
      expect(firstMilestone).toHaveProperty('is_required');
      expect(firstMilestone).toHaveProperty('description');
    });

    it('should generate contribution tracking categories based on project type', () => {
      const testCases = [
        { category: 'software', expectedCategories: ['Development', 'Design', 'Testing', 'Documentation', 'DevOps'] },
        { category: 'creative', expectedCategories: ['Design', 'Content', 'Research', 'Review'] },
        { category: 'business', expectedCategories: ['Strategy', 'Research', 'Communication', 'Management'] },
        { category: 'physical', expectedCategories: ['Design', 'Prototyping', 'Manufacturing', 'Quality'] }
      ];

      testCases.forEach(({ category, expectedCategories }) => {
        const answers = { ...mockAnswers, projectCategory: category };
        const result = mapVentureAnswersToProjectData(answers);
        
        expect(result.contribution_tracking.categories).toBeInstanceOf(Array);
        expectedCategories.forEach(expectedCategory => {
          expect(result.contribution_tracking.categories.some(cat => 
            cat.toLowerCase().includes(expectedCategory.toLowerCase())
          )).toBe(true);
        });
      });
    });

    it('should set correct IP ownership and dispute resolution', () => {
      const result = mapVentureAnswersToProjectData(mockAnswers);
      
      expect(result.ip_ownership).toBe('shared');
      expect(result.dispute_resolution).toBe('mediation');
      expect(result.agreement_type).toBe('business');
    });

    it('should handle missing or invalid data gracefully', () => {
      const incompleteAnswers = {
        ventureName: 'Minimal Test',
        projectCategory: 'unknown',
        timeline: 'invalid'
      };

      const result = mapVentureAnswersToProjectData(incompleteAnswers);
      
      expect(result.name).toBe('Minimal Test');
      expect(result.project_type).toBe('software'); // Default fallback
      expect(result.estimated_duration).toBe(6); // Default fallback
      expect(result.royalty_model).toBeDefined();
      expect(result.revenue_tranches).toBeInstanceOf(Array);
      expect(result.milestones).toBeInstanceOf(Array);
    });

    it('should generate different configurations for different project types', () => {
      const softwareResult = mapVentureAnswersToProjectData({
        ...mockAnswers,
        projectCategory: 'software'
      });

      const creativeResult = mapVentureAnswersToProjectData({
        ...mockAnswers,
        projectCategory: 'creative'
      });

      // Should have different contribution tracking categories
      expect(softwareResult.contribution_tracking.categories)
        .not.toEqual(creativeResult.contribution_tracking.categories);

      // Should have different milestone structures
      expect(softwareResult.milestones[0].name)
        .not.toBe(creativeResult.milestones[0].name);
    });

    it('should include all required fields for legal agreement generation', () => {
      const result = mapVentureAnswersToProjectData(mockAnswers);
      
      // Required for legal agreement
      expect(result).toHaveProperty('name');
      expect(result).toHaveProperty('description');
      expect(result).toHaveProperty('project_type');
      expect(result).toHaveProperty('estimated_duration');
      expect(result).toHaveProperty('start_date');
      expect(result).toHaveProperty('target_audience');
      expect(result).toHaveProperty('royalty_model');
      expect(result).toHaveProperty('revenue_tranches');
      expect(result).toHaveProperty('milestones');
      expect(result).toHaveProperty('contribution_tracking');
      expect(result).toHaveProperty('ip_ownership');
      expect(result).toHaveProperty('dispute_resolution');
      expect(result).toHaveProperty('agreement_type');
      expect(result).toHaveProperty('tags');
      expect(result).toHaveProperty('max_team_size');
      expect(result).toHaveProperty('notification_frequency');
      expect(result).toHaveProperty('requires_approval');
    });

    it('should generate consistent data across multiple calls', () => {
      const result1 = mapVentureAnswersToProjectData(mockAnswers);
      const result2 = mapVentureAnswersToProjectData(mockAnswers);
      
      // Basic fields should be identical
      expect(result1.name).toBe(result2.name);
      expect(result1.project_type).toBe(result2.project_type);
      expect(result1.estimated_duration).toBe(result2.estimated_duration);
      expect(result1.royalty_model.model_type).toBe(result2.royalty_model.model_type);
      
      // Arrays should have same length and structure
      expect(result1.revenue_tranches.length).toBe(result2.revenue_tranches.length);
      expect(result1.milestones.length).toBe(result2.milestones.length);
    });
  });
});
