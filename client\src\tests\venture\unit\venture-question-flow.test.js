import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import VentureQuestionFlow from '../../../components/venture/VentureQuestionFlow.jsx';

/**
 * Unit Tests for VentureQuestionFlow Component
 * 
 * Tests the question flow logic, state management, and data collection
 * for the venture creation wizard.
 */

// Mock framer-motion to avoid animation issues in tests
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>
  },
  AnimatePresence: ({ children }) => children
}));

// Mock the question step component
vi.mock('../../../components/venture/VentureQuestionStep.jsx', () => ({
  default: ({ question, answer, onAnswer, onNext, isAnswered }) => (
    <div data-testid="question-step">
      <h2>{question?.title}</h2>
      <p>{question?.subtitle}</p>
      {question?.fields?.map((field, index) => (
        <div key={index}>
          <label>{field.label}</label>
          {field.type === 'text' && (
            <input
              data-testid={`field-${field.name}`}
              type="text"
              value={answer[field.name] || ''}
              onChange={(e) => onAnswer(field.name, e.target.value)}
            />
          )}
          {field.type === 'select' && (
            <select
              data-testid={`field-${field.name}`}
              value={answer[field.name] || ''}
              onChange={(e) => onAnswer(field.name, e.target.value)}
            >
              <option value="">Select...</option>
              {field.options?.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          )}
          {field.type === 'textarea' && (
            <textarea
              data-testid={`field-${field.name}`}
              value={answer[field.name] || ''}
              onChange={(e) => onAnswer(field.name, e.target.value)}
            />
          )}
        </div>
      ))}
      <button 
        data-testid="next-button" 
        onClick={onNext}
        disabled={!isAnswered}
      >
        Next
      </button>
    </div>
  )
}));

describe('VentureQuestionFlow Component', () => {
  let mockProps;

  beforeEach(() => {
    mockProps = {
      initialData: {},
      onComplete: vi.fn(),
      onBack: vi.fn(),
      onCancel: vi.fn()
    };
  });

  describe('Component Rendering', () => {
    it('should render the first question on mount', () => {
      render(<VentureQuestionFlow {...mockProps} />);
      
      expect(screen.getByTestId('question-step')).toBeInTheDocument();
      expect(screen.getByText("What's your venture?")).toBeInTheDocument();
      expect(screen.getByText('This defines your project scope and IP ownership')).toBeInTheDocument();
    });

    it('should display step indicator', () => {
      render(<VentureQuestionFlow {...mockProps} />);
      
      expect(screen.getByText('Step 1/5')).toBeInTheDocument();
    });

    it('should render cancel button', () => {
      render(<VentureQuestionFlow {...mockProps} />);
      
      const cancelButton = screen.getByText('Cancel');
      expect(cancelButton).toBeInTheDocument();
    });
  });

  describe('Question Navigation', () => {
    it('should advance to next question when answered', async () => {
      render(<VentureQuestionFlow {...mockProps} />);
      
      // Fill in required fields for question 1
      const nameInput = screen.getByTestId('field-ventureName');
      const categorySelect = screen.getByTestId('field-projectCategory');
      const descriptionTextarea = screen.getByTestId('field-ventureDescription');
      
      fireEvent.change(nameInput, { target: { value: 'Test Venture' } });
      fireEvent.change(categorySelect, { target: { value: 'software' } });
      fireEvent.change(descriptionTextarea, { target: { value: 'Test description' } });
      
      // Click next
      const nextButton = screen.getByTestId('next-button');
      expect(nextButton).not.toBeDisabled();
      fireEvent.click(nextButton);
      
      await waitFor(() => {
        expect(screen.getByText('Step 2/5')).toBeInTheDocument();
      });
    });

    it('should prevent advancing without required answers', () => {
      render(<VentureQuestionFlow {...mockProps} />);
      
      const nextButton = screen.getByTestId('next-button');
      expect(nextButton).toBeDisabled();
    });

    it('should allow going back to previous question', async () => {
      render(<VentureQuestionFlow {...mockProps} />);
      
      // Complete first question
      const nameInput = screen.getByTestId('field-ventureName');
      fireEvent.change(nameInput, { target: { value: 'Test Venture' } });
      
      const categorySelect = screen.getByTestId('field-projectCategory');
      fireEvent.change(categorySelect, { target: { value: 'software' } });
      
      const descriptionTextarea = screen.getByTestId('field-ventureDescription');
      fireEvent.change(descriptionTextarea, { target: { value: 'Test description' } });
      
      fireEvent.click(screen.getByTestId('next-button'));
      
      await waitFor(() => {
        expect(screen.getByText('Step 2/5')).toBeInTheDocument();
      });
      
      // Go back
      const backButton = screen.getByText('Back');
      fireEvent.click(backButton);
      
      await waitFor(() => {
        expect(screen.getByText('Step 1/5')).toBeInTheDocument();
      });
    });

    it('should call onBack when going back from first question', () => {
      render(<VentureQuestionFlow {...mockProps} />);
      
      const backButton = screen.getByText('Back');
      fireEvent.click(backButton);
      
      expect(mockProps.onBack).toHaveBeenCalled();
    });
  });

  describe('Data Collection', () => {
    it('should collect and maintain form data across questions', async () => {
      render(<VentureQuestionFlow {...mockProps} />);
      
      // Fill question 1
      fireEvent.change(screen.getByTestId('field-ventureName'), { 
        target: { value: 'Test Venture' } 
      });
      fireEvent.change(screen.getByTestId('field-projectCategory'), { 
        target: { value: 'software' } 
      });
      fireEvent.change(screen.getByTestId('field-ventureDescription'), { 
        target: { value: 'Test description' } 
      });
      
      fireEvent.click(screen.getByTestId('next-button'));
      
      await waitFor(() => {
        expect(screen.getByText('Step 2/5')).toBeInTheDocument();
      });
      
      // Go back and verify data is preserved
      fireEvent.click(screen.getByText('Back'));
      
      await waitFor(() => {
        expect(screen.getByTestId('field-ventureName')).toHaveValue('Test Venture');
        expect(screen.getByTestId('field-projectCategory')).toHaveValue('software');
        expect(screen.getByTestId('field-ventureDescription')).toHaveValue('Test description');
      });
    });

    it('should initialize with provided initial data', () => {
      const initialData = {
        ventureName: 'Pre-filled Venture',
        projectCategory: 'creative'
      };
      
      render(<VentureQuestionFlow {...mockProps} initialData={initialData} />);
      
      expect(screen.getByTestId('field-ventureName')).toHaveValue('Pre-filled Venture');
      expect(screen.getByTestId('field-projectCategory')).toHaveValue('creative');
    });

    it('should complete flow and call onComplete with collected data', async () => {
      render(<VentureQuestionFlow {...mockProps} />);
      
      // Mock completing all 5 questions
      const questionsData = [
        {
          ventureName: 'Complete Test Venture',
          projectCategory: 'software',
          ventureDescription: 'Complete test description'
        },
        {
          timeline: 'medium',
          targetAudience: 'business'
        },
        {
          budget: 'bootstrapped',
          revenueSharing: 'contribution'
        },
        {
          successMetrics: 'revenue',
          ventureTags: ['productivity']
        },
        {
          agreementType: 'business',
          ipOwnership: 'shared',
          disputeResolution: 'mediation'
        }
      ];
      
      // Simulate completing each question
      for (let i = 0; i < questionsData.length; i++) {
        const questionData = questionsData[i];
        
        // Fill fields for current question
        Object.entries(questionData).forEach(([fieldName, value]) => {
          const field = screen.queryByTestId(`field-${fieldName}`);
          if (field) {
            fireEvent.change(field, { target: { value } });
          }
        });
        
        // Click next (or complete on last question)
        const nextButton = screen.getByTestId('next-button');
        fireEvent.click(nextButton);
        
        if (i < questionsData.length - 1) {
          await waitFor(() => {
            expect(screen.getByText(`Step ${i + 2}/5`)).toBeInTheDocument();
          });
        }
      }
      
      // Should call onComplete with all collected data
      await waitFor(() => {
        expect(mockProps.onComplete).toHaveBeenCalledWith(
          expect.objectContaining({
            ventureName: 'Complete Test Venture',
            projectCategory: 'software',
            timeline: 'medium',
            budget: 'bootstrapped',
            successMetrics: 'revenue',
            agreementType: 'business'
          })
        );
      });
    });
  });

  describe('Question Validation', () => {
    it('should validate required fields before allowing progression', () => {
      render(<VentureQuestionFlow {...mockProps} />);
      
      // Try to advance without filling required fields
      const nextButton = screen.getByTestId('next-button');
      expect(nextButton).toBeDisabled();
      
      // Fill only some required fields
      fireEvent.change(screen.getByTestId('field-ventureName'), { 
        target: { value: 'Test' } 
      });
      expect(nextButton).toBeDisabled();
      
      // Fill all required fields
      fireEvent.change(screen.getByTestId('field-projectCategory'), { 
        target: { value: 'software' } 
      });
      fireEvent.change(screen.getByTestId('field-ventureDescription'), { 
        target: { value: 'Description' } 
      });
      
      expect(nextButton).not.toBeDisabled();
    });

    it('should handle conditional questions correctly', async () => {
      render(<VentureQuestionFlow {...mockProps} />);
      
      // Complete first question with specific category
      fireEvent.change(screen.getByTestId('field-ventureName'), { 
        target: { value: 'Test Venture' } 
      });
      fireEvent.change(screen.getByTestId('field-projectCategory'), { 
        target: { value: 'software' } 
      });
      fireEvent.change(screen.getByTestId('field-ventureDescription'), { 
        target: { value: 'Test description' } 
      });
      
      fireEvent.click(screen.getByTestId('next-button'));
      
      await waitFor(() => {
        expect(screen.getByText('Step 2/5')).toBeInTheDocument();
      });
      
      // Should show appropriate fields for software category
      expect(screen.getByText('How will you work together?')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('should handle cancel action', () => {
      render(<VentureQuestionFlow {...mockProps} />);
      
      const cancelButton = screen.getByText('Cancel');
      fireEvent.click(cancelButton);
      
      expect(mockProps.onCancel).toHaveBeenCalled();
    });

    it('should handle missing question data gracefully', () => {
      // This tests robustness when question configuration is incomplete
      expect(() => {
        render(<VentureQuestionFlow {...mockProps} />);
      }).not.toThrow();
    });

    it('should maintain state consistency during rapid navigation', async () => {
      render(<VentureQuestionFlow {...mockProps} />);
      
      // Fill first question
      fireEvent.change(screen.getByTestId('field-ventureName'), { 
        target: { value: 'Rapid Test' } 
      });
      fireEvent.change(screen.getByTestId('field-projectCategory'), { 
        target: { value: 'software' } 
      });
      fireEvent.change(screen.getByTestId('field-ventureDescription'), { 
        target: { value: 'Rapid description' } 
      });
      
      // Rapidly click next and back
      fireEvent.click(screen.getByTestId('next-button'));
      await waitFor(() => screen.getByText('Step 2/5'));
      
      fireEvent.click(screen.getByText('Back'));
      await waitFor(() => screen.getByText('Step 1/5'));
      
      // Data should still be preserved
      expect(screen.getByTestId('field-ventureName')).toHaveValue('Rapid Test');
    });
  });
});
