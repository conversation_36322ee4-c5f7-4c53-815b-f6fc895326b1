import { render } from '@testing-library/react'
import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from 'react-router-dom'
import { UserContext } from '../../../contexts/supabase-auth.context'

// Test wrapper with all necessary providers
export const renderWithProviders = (ui, options = {}) => {
  const {
    user = mockUser,
    ...renderOptions
  } = options

  const Wrapper = ({ children }) => React.createElement(
    BrowserRouter,
    null,
    React.createElement(
      UserContext.Provider,
      { value: { currentUser: user } },
      children
    )
  )

  return render(ui, { wrapper: Wrapper, ...renderOptions })
}

// Mock user data
export const mockUser = {
  id: 'test-user-id',
  email: '<EMAIL>',
  user_metadata: {
    full_name: 'Test User'
  }
}

// Mock alliance data
export const mockAlliance = {
  id: 'test-alliance-id',
  name: 'Test Alliance',
  description: 'A test alliance for testing purposes'
}

// Helper to wait for async operations
export const waitFor = (ms = 100) => new Promise(resolve => setTimeout(resolve, ms))

// Helper to create mock functions with return values
export const createMockFunction = (returnValue) => {
  const fn = vi.fn()
  if (returnValue !== undefined) {
    fn.mockReturnValue(returnValue)
  }
  return fn
}

// Helper to simulate user interactions
export const simulateUserInput = async (element, value) => {
  const { fireEvent } = await import('@testing-library/react')
  fireEvent.change(element, { target: { value } })
  fireEvent.blur(element)
}

// Helper to simulate button clicks
export const simulateClick = async (element) => {
  const { fireEvent } = await import('@testing-library/react')
  fireEvent.click(element)
}

// Helper to check if element has specific classes
export const hasClasses = (element, classes) => {
  return classes.every(className => element.classList.contains(className))
}

// Helper to wait for element to appear
export const waitForElement = async (getByTestId, testId, timeout = 1000) => {
  const { waitFor } = await import('@testing-library/react')
  return waitFor(() => getByTestId(testId), { timeout })
}

// Helper to create venture question answers
export const createVentureAnswers = (overrides = {}) => ({
  ventureName: 'Test Venture',
  projectCategory: 'software',
  ventureDescription: 'A test venture for testing purposes',
  timeline: 'medium',
  targetAudience: 'business',
  budget: 'bootstrapped',
  revenueSharing: 'contribution',
  successMetrics: 'revenue',
  ventureTags: ['test', 'software'],
  ventureIcon: '🚀',
  agreementType: 'business',
  ipOwnership: 'shared',
  disputeResolution: 'discussion',
  ...overrides
})

// Helper to create expected project data
export const createExpectedProjectData = (answers) => ({
  name: answers.ventureName,
  title: answers.ventureName,
  description: answers.ventureDescription,
  project_type: answers.projectCategory,
  estimated_duration: getTimelineInMonths(answers.timeline),
  start_date: expect.any(Date),
  is_public: true,
  icon: answers.ventureIcon,
  tags: answers.ventureTags,
  target_audience: answers.targetAudience,
  royalty_model: expect.objectContaining({
    model_type: expect.any(String)
  }),
  revenue_tranches: expect.any(Array),
  contribution_tracking: expect.objectContaining({
    categories: expect.any(Array)
  }),
  milestones: expect.any(Array)
})

// Helper to get timeline in months
const getTimelineInMonths = (timeline) => {
  const timelineMap = {
    'quick': 1,
    'short': 2,
    'medium': 6,
    'long': 12
  }
  return timelineMap[timeline] || 6
}
