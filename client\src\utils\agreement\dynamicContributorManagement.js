/**
 * Dynamic Contributor Management System
 * 
 * Supports scalable software ventures with:
 * - Variable contributor pools that change over time
 * - Tranche-based revenue sharing by release periods
 * - Contribution point systems for fair revenue distribution
 * - Future contributor additions without renegotiating existing agreements
 * - Time-bounded revenue sharing for active contributors only
 */

// ============================================================================
// DYNAMIC CONTRIBUTOR TYPES AND CONFIGURATIONS
// ============================================================================

export const CONTRIBUTOR_TYPES = {
  CORE_TEAM: 'core_team',           // Fixed team with guaranteed revenue shares
  GIGWORK: 'gigwork',               // Dynamic contributors from platform
  ADVISOR: 'advisor',               // Advisory role with limited revenue share
  INVESTOR: 'investor',             // Financial contributor with equity/revenue rights
  TEMPORARY: 'temporary'            // Short-term contributors for specific tasks
};

export const PARTICIPATION_MODELS = {
  CONTINUOUS: 'continuous',         // Active throughout project lifecycle
  TRANCHE_BASED: 'tranche_based',   // Active only during specific release periods
  MILESTONE_BASED: 'milestone_based', // Active for specific milestones
  TASK_BASED: 'task_based'          // Active for individual tasks/features
};

export const REVENUE_CALCULATION_METHODS = {
  FIXED_PERCENTAGE: 'fixed_percentage',     // Traditional fixed revenue shares
  CONTRIBUTION_POINTS: 'contribution_points', // Based on actual work contribution
  HYBRID: 'hybrid',                         // Combination of fixed + contribution-based
  TRANCHE_WEIGHTED: 'tranche_weighted'      // Weighted by tranche participation
};

export const TRANCHE_TYPES = {
  RELEASE: 'release',               // Major product releases (v1.0, v2.0, etc.)
  SPRINT: 'sprint',                 // Development sprints (2-4 weeks)
  MILESTONE: 'milestone',           // Project milestones
  QUARTER: 'quarter',               // Quarterly periods
  CUSTOM: 'custom'                  // Custom time periods
};

// ============================================================================
// DYNAMIC CONTRIBUTOR MANAGEMENT CLASS
// ============================================================================

export class DynamicContributorManagement {
  constructor() {
    this.ventures = new Map();
    this.contributorPools = new Map();
    this.tranches = new Map();
    this.contributionPoints = new Map();
    this.revenueDistributions = new Map();
    this.agreementTemplates = new Map();
  }

  /**
   * Initialize a scalable venture with dynamic contributor support
   */
  initializeScalableVenture(ventureDefinition) {
    const venture = {
      id: ventureDefinition.id || this.generateVentureId(),
      
      // Basic venture information
      name: ventureDefinition.name,
      description: ventureDefinition.description,
      allianceId: ventureDefinition.allianceId,
      
      // Scalability configuration
      scalabilityConfig: {
        maxContributors: ventureDefinition.maxContributors || 50,
        allowDynamicJoining: ventureDefinition.allowDynamicJoining !== false,
        requireApprovalForNewContributors: ventureDefinition.requireApprovalForNewContributors !== false,
        autoGenerateAgreements: ventureDefinition.autoGenerateAgreements !== false
      },
      
      // Revenue model configuration
      revenueModel: {
        calculationMethod: ventureDefinition.revenueModel?.calculationMethod || REVENUE_CALCULATION_METHODS.HYBRID,
        coreTeamReservedPercentage: ventureDefinition.revenueModel?.coreTeamReservedPercentage || 60,
        gigworkPoolPercentage: ventureDefinition.revenueModel?.gigworkPoolPercentage || 30,
        platformFeePercentage: ventureDefinition.revenueModel?.platformFeePercentage || 10,
        contributionPointsWeight: ventureDefinition.revenueModel?.contributionPointsWeight || 0.7,
        timeParticipationWeight: ventureDefinition.revenueModel?.timeParticipationWeight || 0.3
      },
      
      // Tranche configuration
      trancheConfig: {
        trancheType: ventureDefinition.trancheConfig?.trancheType || TRANCHE_TYPES.RELEASE,
        trancheDuration: ventureDefinition.trancheConfig?.trancheDuration || 90, // days
        revenueDistributionDelay: ventureDefinition.trancheConfig?.revenueDistributionDelay || 30, // days after tranche end
        allowRetrospectiveJoining: ventureDefinition.trancheConfig?.allowRetrospectiveJoining || false
      },
      
      // Contributor pools
      contributorPools: {
        coreTeam: [],
        gigwork: [],
        advisors: [],
        temporary: []
      },
      
      // Active tranches
      activeTranches: [],
      completedTranches: [],
      
      // Agreement management
      agreementTemplates: {
        coreTeamTemplate: null,
        gigworkTemplate: null,
        advisorTemplate: null
      },
      
      // Status and metadata
      status: 'planning',
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    this.ventures.set(venture.id, venture);
    
    // Initialize core team if provided
    if (ventureDefinition.coreTeam) {
      this.addCoreTeamMembers(venture.id, ventureDefinition.coreTeam);
    }
    
    // Create initial tranche if auto-start is enabled
    if (ventureDefinition.autoStartFirstTranche) {
      this.createTranche(venture.id, {
        name: 'Initial Development Tranche',
        type: venture.trancheConfig.trancheType,
        startDate: new Date().toISOString(),
        autoStart: true
      });
    }
    
    return venture;
  }

  /**
   * Add core team members with fixed revenue shares
   */
  addCoreTeamMembers(ventureId, coreTeamMembers) {
    const venture = this.ventures.get(ventureId);
    if (!venture) throw new Error(`Venture ${ventureId} not found`);

    coreTeamMembers.forEach(member => {
      const coreTeamMember = {
        id: member.id || this.generateContributorId(),
        email: member.email,
        role: member.role,
        contributorType: CONTRIBUTOR_TYPES.CORE_TEAM,
        participationModel: PARTICIPATION_MODELS.CONTINUOUS,
        
        // Fixed revenue configuration
        fixedRevenueShare: member.revenueShare,
        guaranteedMinimumShare: member.guaranteedMinimumShare || member.revenueShare,
        
        // Permissions and responsibilities
        permissions: member.permissions || this.getDefaultCoreTeamPermissions(),
        responsibilities: member.responsibilities || [],
        
        // IP and legal
        ipRights: member.ipRights || 'co_owner',
        agreementStatus: 'pending',
        
        // Timeline
        joinedAt: new Date().toISOString(),
        isActive: true,
        
        // Contribution tracking
        contributionPoints: 0,
        trancheParticipation: []
      };

      venture.contributorPools.coreTeam.push(coreTeamMember);
    });

    // Validate total core team revenue shares
    const totalCoreShares = venture.contributorPools.coreTeam.reduce(
      (sum, member) => sum + member.fixedRevenueShare, 0
    );

    if (totalCoreShares > venture.revenueModel.coreTeamReservedPercentage) {
      throw new Error(`Core team revenue shares (${totalCoreShares}%) exceed reserved percentage (${venture.revenueModel.coreTeamReservedPercentage}%)`);
    }

    venture.lastUpdated = new Date().toISOString();
  }

  /**
   * Create a new tranche (release period) for the venture
   */
  createTranche(ventureId, trancheDefinition) {
    const venture = this.ventures.get(ventureId);
    if (!venture) throw new Error(`Venture ${ventureId} not found`);

    const tranche = {
      id: trancheDefinition.id || this.generateTrancheId(),
      ventureId,
      
      // Tranche information
      name: trancheDefinition.name,
      description: trancheDefinition.description || '',
      type: trancheDefinition.type || venture.trancheConfig.trancheType,
      version: trancheDefinition.version || this.calculateNextVersion(ventureId),
      
      // Timeline
      startDate: trancheDefinition.startDate || new Date().toISOString(),
      plannedEndDate: trancheDefinition.plannedEndDate || this.calculateTrancheEndDate(venture, trancheDefinition.startDate || new Date().toISOString()),
      actualEndDate: null,
      
      // Revenue configuration
      revenuePool: {
        totalRevenue: 0,
        coreTeamAllocation: 0,
        gigworkAllocation: 0,
        platformFee: 0,
        distributionStatus: 'pending'
      },
      
      // Contributor tracking
      activeContributors: [],
      contributionPoints: new Map(),
      
      // Deliverables and goals
      deliverables: trancheDefinition.deliverables || [],
      goals: trancheDefinition.goals || [],
      
      // Status
      status: trancheDefinition.autoStart ? 'active' : 'planned',
      createdAt: new Date().toISOString()
    };

    this.tranches.set(tranche.id, tranche);
    
    if (tranche.status === 'active') {
      venture.activeTranches.push(tranche.id);
      
      // Auto-add core team members to active tranche
      venture.contributorPools.coreTeam.forEach(member => {
        this.addContributorToTranche(tranche.id, member.id, {
          participationType: 'core_team',
          autoAdded: true
        });
      });
    }

    venture.lastUpdated = new Date().toISOString();
    return tranche;
  }

  /**
   * Add a gigwork contributor to the venture and current active tranche
   */
  addGigworkContributor(ventureId, contributorDefinition) {
    const venture = this.ventures.get(ventureId);
    if (!venture) throw new Error(`Venture ${ventureId} not found`);

    // Check if venture allows dynamic joining
    if (!venture.scalabilityConfig.allowDynamicJoining) {
      throw new Error('This venture does not allow dynamic contributor joining');
    }

    // Check contributor limit
    const totalContributors = Object.values(venture.contributorPools).flat().length;
    if (totalContributors >= venture.scalabilityConfig.maxContributors) {
      throw new Error(`Venture has reached maximum contributor limit (${venture.scalabilityConfig.maxContributors})`);
    }

    const gigworkContributor = {
      id: contributorDefinition.id || this.generateContributorId(),
      email: contributorDefinition.email,
      role: contributorDefinition.role,
      contributorType: CONTRIBUTOR_TYPES.GIGWORK,
      participationModel: contributorDefinition.participationModel || PARTICIPATION_MODELS.TRANCHE_BASED,
      
      // Skills and capabilities
      skills: contributorDefinition.skills || [],
      experienceLevel: contributorDefinition.experienceLevel || 'intermediate',
      hourlyRate: contributorDefinition.hourlyRate || null,
      
      // Revenue configuration (dynamic)
      expectedContributionLevel: contributorDefinition.expectedContributionLevel || 'medium',
      contributionPointsMultiplier: this.calculateContributionMultiplier(contributorDefinition),
      
      // Permissions (limited for gigwork)
      permissions: this.getDefaultGigworkPermissions(),
      responsibilities: contributorDefinition.responsibilities || [],
      
      // IP and legal
      ipRights: contributorDefinition.ipRights || 'contributor',
      agreementStatus: 'pending',
      
      // Timeline
      joinedAt: new Date().toISOString(),
      isActive: true,
      
      // Contribution tracking
      contributionPoints: 0,
      trancheParticipation: [],
      
      // Gigwork specific
      gigworkProfile: {
        platformRating: contributorDefinition.platformRating || null,
        completedProjects: contributorDefinition.completedProjects || 0,
        specializations: contributorDefinition.specializations || []
      }
    };

    venture.contributorPools.gigwork.push(gigworkContributor);

    // Add to active tranches if auto-join is enabled
    venture.activeTranches.forEach(trancheId => {
      this.addContributorToTranche(trancheId, gigworkContributor.id, {
        participationType: 'gigwork',
        autoAdded: true
      });
    });

    // Generate agreement if auto-generation is enabled
    if (venture.scalabilityConfig.autoGenerateAgreements) {
      this.generateGigworkAgreement(ventureId, gigworkContributor.id);
    }

    venture.lastUpdated = new Date().toISOString();
    return gigworkContributor;
  }

  /**
   * Add contributor to a specific tranche
   */
  addContributorToTranche(trancheId, contributorId, options = {}) {
    const tranche = this.tranches.get(trancheId);
    if (!tranche) throw new Error(`Tranche ${trancheId} not found`);

    const participation = {
      contributorId,
      participationType: options.participationType || 'gigwork',
      joinedAt: new Date().toISOString(),
      leftAt: null,
      isActive: true,
      contributionPoints: 0,
      hoursWorked: 0,
      tasksCompleted: 0,
      autoAdded: options.autoAdded || false
    };

    tranche.activeContributors.push(participation);
    tranche.contributionPoints.set(contributorId, 0);
  }

  /**
   * Calculate contribution points for a contributor in a tranche
   */
  recordContribution(trancheId, contributorId, contributionData) {
    const tranche = this.tranches.get(trancheId);
    if (!tranche) throw new Error(`Tranche ${trancheId} not found`);

    const contribution = {
      id: this.generateContributionId(),
      contributorId,
      trancheId,
      
      // Contribution details
      type: contributionData.type, // 'code', 'design', 'testing', 'documentation', etc.
      description: contributionData.description,
      hoursWorked: contributionData.hoursWorked || 0,
      difficultyLevel: contributionData.difficultyLevel || 'medium',
      qualityRating: contributionData.qualityRating || null,
      
      // Point calculation
      basePoints: this.calculateBasePoints(contributionData),
      difficultyMultiplier: this.getDifficultyMultiplier(contributionData.difficultyLevel),
      qualityMultiplier: this.getQualityMultiplier(contributionData.qualityRating),
      finalPoints: 0,
      
      // Metadata
      recordedAt: new Date().toISOString(),
      verifiedBy: contributionData.verifiedBy || null,
      verificationStatus: 'pending'
    };

    // Calculate final points
    contribution.finalPoints = contribution.basePoints * 
                              contribution.difficultyMultiplier * 
                              contribution.qualityMultiplier;

    // Update tranche contribution points
    const currentPoints = tranche.contributionPoints.get(contributorId) || 0;
    tranche.contributionPoints.set(contributorId, currentPoints + contribution.finalPoints);

    // Update contributor participation
    const participation = tranche.activeContributors.find(p => p.contributorId === contributorId);
    if (participation) {
      participation.contributionPoints += contribution.finalPoints;
      participation.hoursWorked += contribution.hoursWorked;
      participation.tasksCompleted += 1;
    }

    return contribution;
  }

  /**
   * Calculate revenue distribution for a completed tranche
   */
  calculateTrancheRevenueDistribution(trancheId, totalRevenue) {
    const tranche = this.tranches.get(trancheId);
    if (!tranche) throw new Error(`Tranche ${trancheId} not found`);

    const venture = this.ventures.get(tranche.ventureId);
    if (!venture) throw new Error(`Venture ${tranche.ventureId} not found`);

    const distribution = {
      trancheId,
      totalRevenue,
      distributionDate: new Date().toISOString(),
      
      // Platform and core allocations
      platformFee: totalRevenue * (venture.revenueModel.platformFeePercentage / 100),
      coreTeamPool: totalRevenue * (venture.revenueModel.coreTeamReservedPercentage / 100),
      gigworkPool: totalRevenue * (venture.revenueModel.gigworkPoolPercentage / 100),
      
      // Individual distributions
      coreTeamDistribution: {},
      gigworkDistribution: {},
      
      // Calculation metadata
      calculationMethod: venture.revenueModel.calculationMethod,
      totalContributionPoints: 0,
      participantCount: tranche.activeContributors.length
    };

    // Calculate total contribution points for the tranche
    distribution.totalContributionPoints = Array.from(tranche.contributionPoints.values())
      .reduce((sum, points) => sum + points, 0);

    // Distribute core team revenue (fixed percentages)
    venture.contributorPools.coreTeam.forEach(member => {
      const memberShare = (member.fixedRevenueShare / venture.revenueModel.coreTeamReservedPercentage) * distribution.coreTeamPool;
      distribution.coreTeamDistribution[member.id] = memberShare;
    });

    // Distribute gigwork revenue (contribution-based)
    if (distribution.totalContributionPoints > 0) {
      tranche.activeContributors.forEach(participation => {
        if (participation.participationType === 'gigwork') {
          const contributorPoints = tranche.contributionPoints.get(participation.contributorId) || 0;
          const pointsShare = contributorPoints / distribution.totalContributionPoints;
          const revenueShare = pointsShare * distribution.gigworkPool;
          
          distribution.gigworkDistribution[participation.contributorId] = revenueShare;
        }
      });
    }

    // Store distribution
    this.revenueDistributions.set(trancheId, distribution);
    
    // Update tranche revenue pool
    tranche.revenuePool = {
      totalRevenue,
      coreTeamAllocation: distribution.coreTeamPool,
      gigworkAllocation: distribution.gigworkPool,
      platformFee: distribution.platformFee,
      distributionStatus: 'calculated'
    };

    return distribution;
  }

  /**
   * Generate dynamic agreement for gigwork contributor
   */
  generateGigworkAgreement(ventureId, contributorId) {
    const venture = this.ventures.get(ventureId);
    if (!venture) throw new Error(`Venture ${ventureId} not found`);

    const contributor = venture.contributorPools.gigwork.find(c => c.id === contributorId);
    if (!contributor) throw new Error(`Gigwork contributor ${contributorId} not found`);

    const agreement = {
      id: this.generateAgreementId(),
      ventureId,
      contributorId,
      
      // Agreement type and template
      agreementType: 'gigwork_contributor',
      templateVersion: '2.0',
      
      // Dynamic terms
      terms: {
        participationModel: contributor.participationModel,
        revenueCalculationMethod: 'contribution_points',
        ipRights: contributor.ipRights,
        
        // Tranche-specific terms
        trancheParticipation: {
          canJoinActiveTranches: true,
          canJoinFutureTranches: true,
          requiresApprovalForNewTranches: venture.scalabilityConfig.requireApprovalForNewContributors,
          automaticTrancheJoining: false
        },
        
        // Revenue terms
        revenueSharing: {
          method: 'contribution_points',
          gigworkPoolPercentage: venture.revenueModel.gigworkPoolPercentage,
          contributionPointsWeight: venture.revenueModel.contributionPointsWeight,
          minimumPayoutThreshold: 50,
          paymentFrequency: 'tranche_completion'
        },
        
        // Termination and flexibility
        termination: {
          canLeaveAnytime: true,
          canBeRemovedForCause: true,
          noticePeriod: 7, // days
          revenueEntitlementOnExit: 'earned_to_date'
        }
      },
      
      // Generated content
      content: this.generateGigworkAgreementContent(venture, contributor),
      
      // Status
      status: 'generated',
      generatedAt: new Date().toISOString(),
      signedAt: null,
      effectiveDate: new Date().toISOString()
    };

    return agreement;
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  calculateContributionMultiplier(contributorDefinition) {
    let multiplier = 1.0;
    
    // Experience level multiplier
    const experienceMultipliers = {
      'junior': 0.8,
      'intermediate': 1.0,
      'senior': 1.3,
      'expert': 1.5
    };
    
    multiplier *= experienceMultipliers[contributorDefinition.experienceLevel] || 1.0;
    
    // Platform rating multiplier
    if (contributorDefinition.platformRating) {
      multiplier *= Math.min(1.5, 0.8 + (contributorDefinition.platformRating / 5) * 0.7);
    }
    
    return Math.round(multiplier * 100) / 100;
  }

  calculateBasePoints(contributionData) {
    const basePointsMap = {
      'code': 10,
      'design': 8,
      'testing': 6,
      'documentation': 4,
      'review': 3,
      'planning': 5
    };
    
    const basePoints = basePointsMap[contributionData.type] || 5;
    const hoursMultiplier = contributionData.hoursWorked || 1;
    
    return basePoints * hoursMultiplier;
  }

  getDifficultyMultiplier(difficultyLevel) {
    const multipliers = {
      'easy': 0.8,
      'medium': 1.0,
      'hard': 1.3,
      'expert': 1.6
    };
    
    return multipliers[difficultyLevel] || 1.0;
  }

  getQualityMultiplier(qualityRating) {
    if (!qualityRating) return 1.0;
    
    // Quality rating 1-5, multiplier 0.7-1.3
    return 0.7 + (qualityRating - 1) * 0.15;
  }

  getDefaultCoreTeamPermissions() {
    return {
      canEditVenture: true,
      canManageTeam: true,
      canCreateMilestones: true,
      canApproveMilestones: true,
      canManageBudget: true,
      canViewAnalytics: true,
      canInviteMembers: true,
      canManageTranches: true
    };
  }

  getDefaultGigworkPermissions() {
    return {
      canEditVenture: false,
      canManageTeam: false,
      canCreateMilestones: false,
      canApproveMilestones: false,
      canManageBudget: false,
      canViewAnalytics: true,
      canInviteMembers: false,
      canManageTranches: false
    };
  }

  calculateTrancheEndDate(venture, startDate) {
    const start = new Date(startDate);
    const endDate = new Date(start.getTime() + (venture.trancheConfig.trancheDuration * 24 * 60 * 60 * 1000));
    return endDate.toISOString();
  }

  calculateNextVersion(ventureId) {
    const venture = this.ventures.get(ventureId);
    const completedCount = venture.completedTranches.length;
    return `v${completedCount + 1}.0`;
  }

  generateGigworkAgreementContent(venture, contributor) {
    return `
DYNAMIC CONTRIBUTOR AGREEMENT

This Dynamic Contributor Agreement is entered into between ${venture.name} and ${contributor.email}.

PARTICIPATION MODEL: ${contributor.participationModel}
REVENUE CALCULATION: Contribution Points Based
TRANCHE PARTICIPATION: Flexible joining/leaving

REVENUE SHARING:
- Gigwork Pool: ${venture.revenueModel.gigworkPoolPercentage}% of total revenue
- Distribution Method: Based on contribution points earned during active tranches
- Payment Frequency: Upon tranche completion

INTELLECTUAL PROPERTY:
- IP Rights: ${contributor.ipRights}
- Attribution: Required for all contributions

FLEXIBILITY:
- Can join/leave tranches with ${venture.scalabilityConfig.requireApprovalForNewContributors ? 'approval' : 'no approval required'}
- Revenue earned only for tranches actively participated in
- No guaranteed minimum revenue share

This agreement automatically adapts to venture scaling and new contributor additions.
    `.trim();
  }

  // ID Generators
  generateVentureId() { return 'venture_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9); }
  generateContributorId() { return 'contributor_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9); }
  generateTrancheId() { return 'tranche_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9); }
  generateContributionId() { return 'contribution_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9); }
  generateAgreementId() { return 'agreement_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9); }
}

// Export the dynamic contributor management system
export const dynamicContributorManagement = new DynamicContributorManagement();
