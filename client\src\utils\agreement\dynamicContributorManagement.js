/**
 * Dynamic Contributor Management System
 * 
 * Supports scalable software ventures with:
 * - Variable contributor pools that change over time
 * - Tranche-based revenue sharing by release periods
 * - Contribution point systems for fair revenue distribution
 * - Future contributor additions without renegotiating existing agreements
 * - Time-bounded revenue sharing for active contributors only
 */

// ============================================================================
// DYNAMIC CONTRIBUTOR TYPES AND CONFIGURATIONS
// ============================================================================

export const CONTRIBUTOR_TYPES = {
  CORE_TEAM: 'core_team',           // Fixed team with guaranteed revenue shares
  GIGWORK: 'gigwork',               // Dynamic contributors from platform
  ADVISOR: 'advisor',               // Advisory role with limited revenue share
  INVESTOR: 'investor',             // Financial contributor with equity/revenue rights
  TEMPORARY: 'temporary'            // Short-term contributors for specific tasks
};

export const PARTICIPATION_MODELS = {
  CONTINUOUS: 'continuous',         // Active throughout project lifecycle
  TRANCHE_BASED: 'tranche_based',   // Active only during specific release periods
  MILESTONE_BASED: 'milestone_based', // Active for specific milestones
  TASK_BASED: 'task_based'          // Active for individual tasks/features
};

export const REVENUE_CALCULATION_METHODS = {
  FIXED_PERCENTAGE: 'fixed_percentage',     // Traditional fixed revenue shares
  CONTRIBUTION_POINTS: 'contribution_points', // Based on actual work contribution (DEFAULT)
  HYBRID: 'hybrid',                         // Combination of fixed + contribution-based
  TRANCHE_WEIGHTED: 'tranche_weighted'      // Weighted by tranche participation
};

export const REVENUE_MODEL_PRESETS = {
  UNIFIED_POOL: {
    name: 'Unified Pool (Recommended)',
    description: 'All contributors earn based on contribution points - maximum fairness and simplicity',
    calculationMethod: REVENUE_CALCULATION_METHODS.CONTRIBUTION_POINTS,
    coreTeamReservedPercentage: 0,
    gigworkPoolPercentage: 90,
    platformFeePercentage: 10,
    contributionPointsWeight: 1.0,
    timeParticipationWeight: 0.0,
    benefits: [
      'Pure meritocracy - best performers earn most',
      'Simple to understand and manage',
      'Motivates all contributors equally',
      'Scales naturally with team growth',
      'No artificial hierarchies'
    ]
  },

  HYBRID_SAFETY_NET: {
    name: 'Hybrid with Founder Safety Net',
    description: 'Mostly contribution-based with minimum guarantees for founders',
    calculationMethod: REVENUE_CALCULATION_METHODS.HYBRID,
    coreTeamReservedPercentage: 10,
    gigworkPoolPercentage: 80,
    platformFeePercentage: 10,
    contributionPointsWeight: 0.8,
    timeParticipationWeight: 0.2,
    benefits: [
      'Merit-based with founder protection',
      'Reduces founder financial risk',
      'Still rewards high performers',
      'Good for early-stage ventures'
    ]
  },

  SEPARATE_POOLS: {
    name: 'Separate Pools (Traditional)',
    description: 'Fixed percentages for core team, contribution-based for gigwork',
    calculationMethod: REVENUE_CALCULATION_METHODS.HYBRID,
    coreTeamReservedPercentage: 50,
    gigworkPoolPercentage: 40,
    platformFeePercentage: 10,
    contributionPointsWeight: 0.7,
    timeParticipationWeight: 0.3,
    benefits: [
      'Predictable founder income',
      'Clear hierarchy structure',
      'Traditional business model',
      'Good for risk-averse founders'
    ]
  }
};

export const TRANCHE_TYPES = {
  RELEASE: 'release',               // Major product releases (v1.0, v2.0, etc.)
  SPRINT: 'sprint',                 // Development sprints (2-4 weeks)
  MILESTONE: 'milestone',           // Project milestones
  QUARTER: 'quarter',               // Quarterly periods
  CUSTOM: 'custom'                  // Custom time periods
};

// ============================================================================
// DYNAMIC CONTRIBUTOR MANAGEMENT CLASS
// ============================================================================

export class DynamicContributorManagement {
  constructor() {
    this.ventures = new Map();
    this.contributorPools = new Map();
    this.tranches = new Map();
    this.contributionPoints = new Map();
    this.revenueDistributions = new Map();
    this.agreementTemplates = new Map();
  }

  /**
   * Apply revenue model preset to venture definition
   */
  applyRevenueModelPreset(ventureDefinition, presetName = 'UNIFIED_POOL') {
    const preset = REVENUE_MODEL_PRESETS[presetName];
    if (!preset) {
      throw new Error(`Unknown revenue model preset: ${presetName}`);
    }

    // Apply preset values, but allow overrides from ventureDefinition
    ventureDefinition.revenueModel = {
      ...preset,
      ...ventureDefinition.revenueModel
    };

    return ventureDefinition;
  }

  /**
   * Get available revenue model presets
   */
  getRevenueModelPresets() {
    return REVENUE_MODEL_PRESETS;
  }

  /**
   * Initialize a scalable venture with dynamic contributor support
   */
  initializeScalableVenture(ventureDefinition) {
    const venture = {
      id: ventureDefinition.id || this.generateVentureId(),
      
      // Basic venture information
      name: ventureDefinition.name,
      description: ventureDefinition.description,
      allianceId: ventureDefinition.allianceId,
      
      // Scalability configuration
      scalabilityConfig: {
        maxContributors: ventureDefinition.maxContributors || 50,
        allowDynamicJoining: ventureDefinition.allowDynamicJoining !== false,
        requireApprovalForNewContributors: ventureDefinition.requireApprovalForNewContributors !== false,
        autoGenerateAgreements: ventureDefinition.autoGenerateAgreements !== false
      },
      
      // Revenue model configuration - UNIFIED POOL AS DEFAULT
      revenueModel: {
        calculationMethod: ventureDefinition.revenueModel?.calculationMethod || REVENUE_CALCULATION_METHODS.CONTRIBUTION_POINTS,
        coreTeamReservedPercentage: ventureDefinition.revenueModel?.coreTeamReservedPercentage || 0,
        gigworkPoolPercentage: ventureDefinition.revenueModel?.gigworkPoolPercentage || 90,
        platformFeePercentage: ventureDefinition.revenueModel?.platformFeePercentage || 10,
        contributionPointsWeight: ventureDefinition.revenueModel?.contributionPointsWeight || 1.0,
        timeParticipationWeight: ventureDefinition.revenueModel?.timeParticipationWeight || 0.0
      },
      
      // Tranche configuration
      trancheConfig: {
        trancheType: ventureDefinition.trancheConfig?.trancheType || TRANCHE_TYPES.RELEASE,
        trancheDuration: ventureDefinition.trancheConfig?.trancheDuration || 90, // days
        revenueDistributionDelay: ventureDefinition.trancheConfig?.revenueDistributionDelay || 30, // days after tranche end
        allowRetrospectiveJoining: ventureDefinition.trancheConfig?.allowRetrospectiveJoining || false
      },
      
      // Contributor pools
      contributorPools: {
        coreTeam: [],
        gigwork: [],
        advisors: [],
        temporary: []
      },
      
      // Active tranches
      activeTranches: [],
      completedTranches: [],
      
      // Agreement management
      agreementTemplates: {
        coreTeamTemplate: null,
        gigworkTemplate: null,
        advisorTemplate: null
      },
      
      // Status and metadata
      status: 'planning',
      createdAt: new Date().toISOString(),
      lastUpdated: new Date().toISOString()
    };

    this.ventures.set(venture.id, venture);
    
    // Initialize core team if provided
    if (ventureDefinition.coreTeam) {
      this.addCoreTeamMembers(venture.id, ventureDefinition.coreTeam);
    }
    
    // Create initial tranche if auto-start is enabled
    if (ventureDefinition.autoStartFirstTranche) {
      this.createTranche(venture.id, {
        name: 'Initial Development Tranche',
        type: venture.trancheConfig.trancheType,
        startDate: new Date().toISOString(),
        autoStart: true
      });
    }
    
    return venture;
  }

  /**
   * Add core team members with fixed revenue shares
   */
  addCoreTeamMembers(ventureId, coreTeamMembers) {
    const venture = this.ventures.get(ventureId);
    if (!venture) throw new Error(`Venture ${ventureId} not found`);

    coreTeamMembers.forEach(member => {
      const coreTeamMember = {
        id: member.id || this.generateContributorId(),
        email: member.email,
        role: member.role,
        contributorType: CONTRIBUTOR_TYPES.CORE_TEAM,
        participationModel: PARTICIPATION_MODELS.CONTINUOUS,
        
        // Revenue configuration (unified pool uses 0 for fixed shares)
        fixedRevenueShare: venture.revenueModel.calculationMethod === REVENUE_CALCULATION_METHODS.CONTRIBUTION_POINTS ? 0 : member.revenueShare,
        guaranteedMinimumShare: member.guaranteedMinimumShare || 0,
        
        // Permissions and responsibilities
        permissions: member.permissions || this.getDefaultCoreTeamPermissions(),
        responsibilities: member.responsibilities || [],
        
        // IP and legal
        ipRights: member.ipRights || 'co_owner',
        agreementStatus: 'pending',
        
        // Timeline
        joinedAt: new Date().toISOString(),
        isActive: true,
        
        // Contribution tracking
        contributionPoints: 0,
        trancheParticipation: []
      };

      venture.contributorPools.coreTeam.push(coreTeamMember);
    });

    // Validate total core team revenue shares (only for non-unified pool models)
    if (venture.revenueModel.calculationMethod !== REVENUE_CALCULATION_METHODS.CONTRIBUTION_POINTS) {
      const totalCoreShares = venture.contributorPools.coreTeam.reduce(
        (sum, member) => sum + member.fixedRevenueShare, 0
      );

      if (totalCoreShares > venture.revenueModel.coreTeamReservedPercentage) {
        throw new Error(`Core team revenue shares (${totalCoreShares}%) exceed reserved percentage (${venture.revenueModel.coreTeamReservedPercentage}%)`);
      }
    }

    venture.lastUpdated = new Date().toISOString();
  }

  /**
   * Create a new tranche (release period) for the venture
   */
  createTranche(ventureId, trancheDefinition) {
    const venture = this.ventures.get(ventureId);
    if (!venture) throw new Error(`Venture ${ventureId} not found`);

    const tranche = {
      id: trancheDefinition.id || this.generateTrancheId(),
      ventureId,
      
      // Tranche information
      name: trancheDefinition.name,
      description: trancheDefinition.description || '',
      type: trancheDefinition.type || venture.trancheConfig.trancheType,
      version: trancheDefinition.version || this.calculateNextVersion(ventureId),
      
      // Timeline
      startDate: trancheDefinition.startDate || new Date().toISOString(),
      plannedEndDate: trancheDefinition.plannedEndDate || this.calculateTrancheEndDate(venture, trancheDefinition.startDate || new Date().toISOString()),
      actualEndDate: null,
      
      // Revenue configuration
      revenuePool: {
        totalRevenue: 0,
        coreTeamAllocation: 0,
        gigworkAllocation: 0,
        platformFee: 0,
        distributionStatus: 'pending'
      },
      
      // Contributor tracking
      activeContributors: [],
      contributionPoints: new Map(),
      
      // Deliverables and goals
      deliverables: trancheDefinition.deliverables || [],
      goals: trancheDefinition.goals || [],
      
      // Status
      status: trancheDefinition.autoStart ? 'active' : 'planned',
      createdAt: new Date().toISOString()
    };

    this.tranches.set(tranche.id, tranche);
    
    if (tranche.status === 'active') {
      venture.activeTranches.push(tranche.id);
      
      // Auto-add core team members to active tranche
      venture.contributorPools.coreTeam.forEach(member => {
        this.addContributorToTranche(tranche.id, member.id, {
          participationType: 'core_team',
          autoAdded: true
        });
      });
    }

    venture.lastUpdated = new Date().toISOString();
    return tranche;
  }

  /**
   * Add a gigwork contributor to the venture and current active tranche
   */
  addGigworkContributor(ventureId, contributorDefinition) {
    const venture = this.ventures.get(ventureId);
    if (!venture) throw new Error(`Venture ${ventureId} not found`);

    // Check if venture allows dynamic joining
    if (!venture.scalabilityConfig.allowDynamicJoining) {
      throw new Error('This venture does not allow dynamic contributor joining');
    }

    // Check contributor limit
    const totalContributors = Object.values(venture.contributorPools).flat().length;
    if (totalContributors >= venture.scalabilityConfig.maxContributors) {
      throw new Error(`Venture has reached maximum contributor limit (${venture.scalabilityConfig.maxContributors})`);
    }

    const gigworkContributor = {
      id: contributorDefinition.id || this.generateContributorId(),
      email: contributorDefinition.email,
      role: contributorDefinition.role,
      contributorType: CONTRIBUTOR_TYPES.GIGWORK,
      participationModel: contributorDefinition.participationModel || PARTICIPATION_MODELS.TRANCHE_BASED,
      
      // Skills and capabilities
      skills: contributorDefinition.skills || [],
      experienceLevel: contributorDefinition.experienceLevel || 'intermediate',
      hourlyRate: contributorDefinition.hourlyRate || null,
      
      // Revenue configuration (dynamic)
      expectedContributionLevel: contributorDefinition.expectedContributionLevel || 'medium',
      contributionPointsMultiplier: this.calculateContributionMultiplier(contributorDefinition),
      
      // Permissions (limited for gigwork)
      permissions: this.getDefaultGigworkPermissions(),
      responsibilities: contributorDefinition.responsibilities || [],
      
      // IP and legal
      ipRights: contributorDefinition.ipRights || 'contributor',
      agreementStatus: 'pending',
      
      // Timeline
      joinedAt: new Date().toISOString(),
      isActive: true,
      
      // Contribution tracking
      contributionPoints: 0,
      trancheParticipation: [],
      
      // Gigwork specific
      gigworkProfile: {
        platformRating: contributorDefinition.platformRating || null,
        completedProjects: contributorDefinition.completedProjects || 0,
        specializations: contributorDefinition.specializations || []
      }
    };

    venture.contributorPools.gigwork.push(gigworkContributor);

    // Add to active tranches if auto-join is enabled
    venture.activeTranches.forEach(trancheId => {
      this.addContributorToTranche(trancheId, gigworkContributor.id, {
        participationType: 'gigwork',
        autoAdded: true
      });
    });

    // Generate agreement if auto-generation is enabled
    if (venture.scalabilityConfig.autoGenerateAgreements) {
      this.generateGigworkAgreement(ventureId, gigworkContributor.id);
    }

    venture.lastUpdated = new Date().toISOString();
    return gigworkContributor;
  }

  /**
   * Add contributor to a specific tranche
   */
  addContributorToTranche(trancheId, contributorId, options = {}) {
    const tranche = this.tranches.get(trancheId);
    if (!tranche) throw new Error(`Tranche ${trancheId} not found`);

    const participation = {
      contributorId,
      participationType: options.participationType || 'gigwork',
      joinedAt: new Date().toISOString(),
      leftAt: null,
      isActive: true,
      contributionPoints: 0,
      hoursWorked: 0,
      tasksCompleted: 0,
      autoAdded: options.autoAdded || false
    };

    tranche.activeContributors.push(participation);
    tranche.contributionPoints.set(contributorId, 0);
  }

  /**
   * Calculate contribution points for a contributor in a tranche
   */
  recordContribution(trancheId, contributorId, contributionData) {
    const tranche = this.tranches.get(trancheId);
    if (!tranche) throw new Error(`Tranche ${trancheId} not found`);

    const contribution = {
      id: this.generateContributionId(),
      contributorId,
      trancheId,
      
      // Contribution details
      type: contributionData.type, // 'code', 'design', 'testing', 'documentation', etc.
      description: contributionData.description,
      hoursWorked: contributionData.hoursWorked || 0,
      difficultyLevel: contributionData.difficultyLevel || 'medium',
      qualityRating: contributionData.qualityRating || null,
      
      // Point calculation
      basePoints: this.calculateBasePoints(contributionData),
      difficultyMultiplier: this.getDifficultyMultiplier(contributionData.difficultyLevel),
      qualityMultiplier: this.getQualityMultiplier(contributionData.qualityRating),
      finalPoints: 0,
      
      // Metadata
      recordedAt: new Date().toISOString(),
      verifiedBy: contributionData.verifiedBy || null,
      verificationStatus: 'pending'
    };

    // Calculate final points
    contribution.finalPoints = contribution.basePoints * 
                              contribution.difficultyMultiplier * 
                              contribution.qualityMultiplier;

    // Update tranche contribution points
    const currentPoints = tranche.contributionPoints.get(contributorId) || 0;
    tranche.contributionPoints.set(contributorId, currentPoints + contribution.finalPoints);

    // Update contributor participation
    const participation = tranche.activeContributors.find(p => p.contributorId === contributorId);
    if (participation) {
      participation.contributionPoints += contribution.finalPoints;
      participation.hoursWorked += contribution.hoursWorked;
      participation.tasksCompleted += 1;
    }

    return contribution;
  }

  /**
   * Calculate revenue distribution for a completed tranche
   */
  calculateTrancheRevenueDistribution(trancheId, totalRevenue) {
    const tranche = this.tranches.get(trancheId);
    if (!tranche) throw new Error(`Tranche ${trancheId} not found`);

    const venture = this.ventures.get(tranche.ventureId);
    if (!venture) throw new Error(`Venture ${tranche.ventureId} not found`);

    const distribution = {
      trancheId,
      totalRevenue,
      distributionDate: new Date().toISOString(),
      
      // Platform and core allocations
      platformFee: totalRevenue * (venture.revenueModel.platformFeePercentage / 100),
      coreTeamPool: totalRevenue * (venture.revenueModel.coreTeamReservedPercentage / 100),
      gigworkPool: totalRevenue * (venture.revenueModel.gigworkPoolPercentage / 100),
      
      // Individual distributions
      coreTeamDistribution: {},
      gigworkDistribution: {},
      
      // Calculation metadata
      calculationMethod: venture.revenueModel.calculationMethod,
      totalContributionPoints: 0,
      participantCount: tranche.activeContributors.length
    };

    // Calculate total contribution points for the tranche
    distribution.totalContributionPoints = Array.from(tranche.contributionPoints.values())
      .reduce((sum, points) => sum + points, 0);

    // UNIFIED POOL APPROACH: Distribute revenue based on calculation method
    if (venture.revenueModel.calculationMethod === REVENUE_CALCULATION_METHODS.CONTRIBUTION_POINTS) {
      // UNIFIED POOL: All contributors (core team + gigwork) earn based on contribution points
      if (distribution.totalContributionPoints > 0) {
        tranche.activeContributors.forEach(participation => {
          const contributorPoints = tranche.contributionPoints.get(participation.contributorId) || 0;
          const pointsShare = contributorPoints / distribution.totalContributionPoints;
          const revenueShare = pointsShare * distribution.gigworkPool; // gigworkPool is actually the unified pool

          // Put all contributors in gigworkDistribution (it's really the unified pool)
          distribution.gigworkDistribution[participation.contributorId] = revenueShare;
        });
      }
    } else {
      // SEPARATE POOLS APPROACH: Traditional fixed + contribution-based split

      // Distribute core team revenue (fixed percentages)
      if (venture.revenueModel.coreTeamReservedPercentage > 0) {
        venture.contributorPools.coreTeam.forEach(member => {
          const memberShare = (member.fixedRevenueShare / venture.revenueModel.coreTeamReservedPercentage) * distribution.coreTeamPool;
          distribution.coreTeamDistribution[member.id] = memberShare;
        });
      }

      // Distribute gigwork revenue (contribution-based)
      if (distribution.totalContributionPoints > 0) {
        tranche.activeContributors.forEach(participation => {
          if (participation.participationType === 'gigwork') {
            const contributorPoints = tranche.contributionPoints.get(participation.contributorId) || 0;
            const pointsShare = contributorPoints / distribution.totalContributionPoints;
            const revenueShare = pointsShare * distribution.gigworkPool;

            distribution.gigworkDistribution[participation.contributorId] = revenueShare;
          }
        });
      }
    }

    // Store distribution
    this.revenueDistributions.set(trancheId, distribution);
    
    // Update tranche revenue pool
    tranche.revenuePool = {
      totalRevenue,
      coreTeamAllocation: distribution.coreTeamPool,
      gigworkAllocation: distribution.gigworkPool,
      platformFee: distribution.platformFee,
      distributionStatus: 'calculated'
    };

    return distribution;
  }

  /**
   * Generate dynamic agreement for gigwork contributor
   */
  generateGigworkAgreement(ventureId, contributorId, options = {}) {
    const venture = this.ventures.get(ventureId);
    if (!venture) throw new Error(`Venture ${ventureId} not found`);

    const contributor = venture.contributorPools.gigwork.find(c => c.id === contributorId);
    if (!contributor) throw new Error(`Gigwork contributor ${contributorId} not found`);

    const agreement = {
      id: this.generateAgreementId(),
      ventureId,
      contributorId,
      
      // Agreement type and template
      agreementType: 'gigwork_contributor',
      templateVersion: '2.0',
      
      // Dynamic terms
      terms: {
        participationModel: contributor.participationModel,
        revenueCalculationMethod: 'contribution_points',
        ipRights: contributor.ipRights,
        
        // Tranche-specific terms
        trancheParticipation: {
          canJoinActiveTranches: true,
          canJoinFutureTranches: true,
          requiresApprovalForNewTranches: venture.scalabilityConfig.requireApprovalForNewContributors,
          automaticTrancheJoining: false
        },
        
        // Revenue terms (unified pool language)
        revenueSharing: {
          method: venture.revenueModel.calculationMethod,
          contributorPoolPercentage: venture.revenueModel.gigworkPoolPercentage, // This is the unified pool
          isUnifiedPool: venture.revenueModel.calculationMethod === REVENUE_CALCULATION_METHODS.CONTRIBUTION_POINTS,
          contributionPointsWeight: venture.revenueModel.contributionPointsWeight,
          minimumPayoutThreshold: 50,
          paymentFrequency: 'tranche_completion'
        },
        
        // Termination and flexibility
        termination: {
          canLeaveAnytime: true,
          canBeRemovedForCause: true,
          noticePeriod: 7, // days
          revenueEntitlementOnExit: 'earned_to_date'
        }
      },
      
      // Generated content (use enhanced generator by default for production compliance)
      content: options.useBasicGenerator ?
        this.generateGigworkAgreementContent(venture, contributor) :
        this.generateEnhancedAgreementContent(venture, contributor, options),
      
      // Status
      status: 'generated',
      generatedAt: new Date().toISOString(),
      signedAt: null,
      effectiveDate: new Date().toISOString()
    };

    return agreement;
  }

  // ============================================================================
  // HELPER METHODS
  // ============================================================================

  calculateContributionMultiplier(contributorDefinition) {
    let multiplier = 1.0;
    
    // Experience level multiplier
    const experienceMultipliers = {
      'junior': 0.8,
      'intermediate': 1.0,
      'senior': 1.3,
      'expert': 1.5
    };
    
    multiplier *= experienceMultipliers[contributorDefinition.experienceLevel] || 1.0;
    
    // Platform rating multiplier
    if (contributorDefinition.platformRating) {
      multiplier *= Math.min(1.5, 0.8 + (contributorDefinition.platformRating / 5) * 0.7);
    }
    
    return Math.round(multiplier * 100) / 100;
  }

  calculateBasePoints(contributionData) {
    const basePointsMap = {
      'code': 10,
      'design': 8,
      'testing': 6,
      'documentation': 4,
      'review': 3,
      'planning': 5
    };
    
    const basePoints = basePointsMap[contributionData.type] || 5;
    const hoursMultiplier = contributionData.hoursWorked || 1;
    
    return basePoints * hoursMultiplier;
  }

  getDifficultyMultiplier(difficultyLevel) {
    const multipliers = {
      'easy': 0.8,
      'medium': 1.0,
      'hard': 1.3,
      'expert': 1.6
    };
    
    return multipliers[difficultyLevel] || 1.0;
  }

  getQualityMultiplier(qualityRating) {
    if (!qualityRating) return 1.0;
    
    // Quality rating 1-5, multiplier 0.7-1.3
    return 0.7 + (qualityRating - 1) * 0.15;
  }

  getDefaultCoreTeamPermissions() {
    return {
      canEditVenture: true,
      canManageTeam: true,
      canCreateMilestones: true,
      canApproveMilestones: true,
      canManageBudget: true,
      canViewAnalytics: true,
      canInviteMembers: true,
      canManageTranches: true
    };
  }

  getDefaultGigworkPermissions() {
    return {
      canEditVenture: false,
      canManageTeam: false,
      canCreateMilestones: false,
      canApproveMilestones: false,
      canManageBudget: false,
      canViewAnalytics: true,
      canInviteMembers: false,
      canManageTranches: false
    };
  }

  calculateTrancheEndDate(venture, startDate) {
    const start = new Date(startDate);
    const endDate = new Date(start.getTime() + (venture.trancheConfig.trancheDuration * 24 * 60 * 60 * 1000));
    return endDate.toISOString();
  }

  calculateNextVersion(ventureId) {
    const venture = this.ventures.get(ventureId);
    const completedCount = venture.completedTranches.length;
    return `v${completedCount + 1}.0`;
  }

  generateGigworkAgreementContent(venture, contributor) {
    const isUnifiedPool = venture.revenueModel.calculationMethod === REVENUE_CALCULATION_METHODS.CONTRIBUTION_POINTS;
    const poolDescription = isUnifiedPool
      ? `Unified Contributor Pool: ${venture.revenueModel.gigworkPoolPercentage}% of total revenue shared among ALL contributors`
      : `Gigwork Pool: ${venture.revenueModel.gigworkPoolPercentage}% of total revenue (separate from core team pool)`;

    return `
DYNAMIC CONTRIBUTOR AGREEMENT

This Dynamic Contributor Agreement is entered into between ${venture.name} and ${contributor.email}.

PARTICIPATION MODEL: ${contributor.participationModel}
REVENUE CALCULATION: ${isUnifiedPool ? 'Unified Pool - Pure Contribution Points' : 'Contribution Points Based'}
TRANCHE PARTICIPATION: Flexible joining/leaving

REVENUE SHARING:
- ${poolDescription}
- Distribution Method: Based on contribution points earned during active tranches
- ${isUnifiedPool ? 'Equal treatment: All contributors (including founders) earn based on contribution' : 'Gigwork contributors earn based on contribution points'}
- Payment Frequency: Upon tranche completion

INTELLECTUAL PROPERTY:
- IP Rights: ${contributor.ipRights}
- Attribution: Required for all contributions

FLEXIBILITY:
- Can join/leave tranches with ${venture.scalabilityConfig.requireApprovalForNewContributors ? 'approval' : 'no approval required'}
- Revenue earned only for tranches actively participated in
- ${isUnifiedPool ? 'No guaranteed minimum revenue share - pure meritocracy' : 'No guaranteed minimum revenue share'}

This agreement automatically adapts to venture scaling and new contributor additions.
${isUnifiedPool ? '\nUNIFIED POOL BENEFITS: Maximum fairness, simplicity, and equal opportunity for all contributors.' : ''}
    `.trim();
  }

  /**
   * Generate enhanced agreement content using lawyer template
   */
  generateEnhancedAgreementContent(venture, contributor, options = {}) {
    // Use the lawyer template generator for production-grade agreements
    const isUnifiedPool = venture.revenueModel.calculationMethod === REVENUE_CALCULATION_METHODS.CONTRIBUTION_POINTS;
    const effectiveDate = new Date().toLocaleDateString();

    // Generate comprehensive lawyer-compliant agreement

    return `
# ${venture.name.toUpperCase()}
# CONTRIBUTOR AGREEMENT

This Contributor Agreement (this "Agreement") is effective as of ${effectiveDate}, by and between ${venture.name}, a collaborative venture (the "Company") and ${contributor.email} (the "Contributor").

## Recitals

WHEREAS, the Company desires to procure services of the Contributor, and the Contributor is willing to provide services to the Company, specifically as provided in the project specifications (the "Services") for the consideration as provided in the revenue sharing model (the "Consideration");

WHEREAS, the Contributor shall provide the Services to the Company as an independent contractor on the terms set forth in this Agreement;

WHEREAS, Contributor to the best of his or her knowledge is not legally obligated, whether by entering into an agreement or otherwise, in any way that conflicts with the terms of this Agreement, nor violates any third party's Intellectual Property Rights; and

WHEREAS, the Contributor acknowledges he or she will have access to certain confidential information which is vital to the success of the Company's business, and that the Company has a legitimate business interest in protecting such information.

NOW THEREFORE, in consideration of the foregoing and for other good and valuable consideration (the receipt of which the Contributor hereby acknowledges), the Contributor, intending to be legally bound hereby, agrees with the Company as follows:

## 1. Definitions

The terms below shall have the following meanings when used throughout this Agreement:

(a) **"Background IP"** means the Intellectual Property Rights owned by or licensed to a Party at the Effective Date, and any modifications or improvements thereto.

(b) **"Confidential Information"** shall mean and include all information, whether written or oral, tangible or intangible, of a private, secret, proprietary or confidential nature, of or concerning the Company, its business and operations, including without limitation, any trade secrets, development information, technology, processes, methodologies, business plans, financial data, and any other information that the Company possesses or uses and not releases externally without restriction.

(c) **"Contribution"** means any original Work Product, including any modification of or addition to an existing work product that the Contributor submits to the Company.

(d) **"Developed IP"** means the intellectual property rights created by or on behalf of Contributor in the course of performing the Services, including all intellectual property in and to the Work Product.

(e) **"Work Product"** shall mean and include any and all products, designs, works, original works, discoveries, inventions and improvements and other results of the Contributor's engagement with the Company that may be conceived, developed, produced, prepared, created or contributed to by the Contributor during the period of engagement.

(f) **"Revenue Tranche"** means a designated portion of Revenue that is allocated for distribution to Contributors based on predefined criteria, including time periods and revenue thresholds.

(g) **"Contribution Points"** means the numerical value assigned to Contributor's Contributions based on factors including time committed, task complexity, and overall impact on the Work Product.

(h) **"Intellectual Property Rights"** means all intellectual property rights worldwide, including without limitation, patents, copyrights, trademarks, trade secrets, moral rights, and any applications, registrations, renewals, extensions, continuations, divisions or reissues thereof now or hereafter in force and effect worldwide.

## 2. Revenue Sharing

**Revenue Distribution Model:** ${isUnifiedPool ? 'Unified Contribution Pool' : 'Separate Pool System'}

(a) **Revenue Pool.** ${isUnifiedPool ?
  `All Contributors (including founders) participate in a unified revenue pool representing ${venture.revenueModel.gigworkPoolPercentage}% of total venture revenue. Revenue is distributed based purely on contribution points earned during active participation.` :
  `Contributors participate in a designated pool representing ${venture.revenueModel.gigworkPoolPercentage}% of total venture revenue, separate from core team allocations.`}

(b) **Contribution Points System.** Revenue distribution is calculated based on Contribution Points earned through:
   i. Time committed to project work
   ii. Complexity and difficulty of tasks completed
   iii. Quality of work delivered
   iv. Overall impact on project success

(c) **Payment Frequency.** Revenue distributions occur upon completion of Revenue Tranches, typically aligned with major project milestones or release cycles.

(d) **Minimum Payout.** Minimum payout threshold is $50 per distribution period.

## 3. Treatment of Confidential Information

(a) **Ownership and Implied Rights.** The Contributor acknowledges that all Confidential Information is and shall remain the exclusive property of the Company.

(b) **Use and Disclosure.** The Contributor agrees that at all times during and after termination of engagement, the Contributor shall:
   i. Hold the Confidential Information in the strictest confidence
   ii. Use the Confidential Information solely in connection with engagement with the Company
   iii. Take all precautions necessary to ensure confidentiality
   iv. Observe all security policies implemented by the Company

(c) **Return of Confidential Documents.** The Contributor shall return all Confidential Documents to the Company upon request or termination of engagement.

(d) **Survival.** Confidentiality obligations survive termination for 24 months.

## 4. Ownership of Work Product

(a) **Company Ownership.** All Work Product conceived, created, designed, developed or contributed by the Contributor in connection with providing Services shall be and remain the exclusive property of the Company.

(b) **Background IP License.** If Contributor incorporates Background IP into Company Work Product, the Company is hereby granted a nonexclusive, transferable, royalty-free, fully-paid, irrevocable, perpetual, world-wide license to use such Background IP.

(c) **Assignment of Rights.** The Contributor hereby assigns, transfers and conveys to the Company any and all worldwide right, title and interest in the Work Product.

## 5. Non-Disparagement

The Contributor agrees that during the term of this Agreement and for a period of two (2) years thereafter, the Contributor will not make any public statement or take any public action which disparages or is intended to disparage the Company, its products, services, or reputation.

## 6. Termination

(a) **Termination for Convenience.** Either Party may terminate this Agreement on 30 days' written notice.

(b) **Termination for Cause.** Either Party may terminate immediately for material breach not cured within 30 days of written notice.

(c) **Effects of Termination.** Upon termination, Contributor shall promptly return all Confidential Information and Work Product materials.

## 7. Equitable Remedies

The Contributor acknowledges that any breach of the confidentiality, intellectual property, or non-disparagement provisions of this Agreement would cause irreparable harm to the Company for which monetary damages would be inadequate. Therefore, the Company shall be entitled to seek equitable relief, including injunction and specific performance, without prejudice to any other rights or remedies.

## 8. Assignment

This Agreement and the rights and obligations hereunder may not be assigned by the Contributor without the prior written consent of the Company. The Company may assign this Agreement and its rights and obligations hereunder to any affiliate or in connection with any merger, acquisition, or sale of all or substantially all of its assets.

## 9. Waivers and Amendments

No waiver of any provision of this Agreement shall be effective unless in writing and signed by the party against whom such waiver is sought to be enforced. No amendment or modification of this Agreement shall be effective unless in writing and signed by both parties.

## 10. Survival

The provisions of this Agreement relating to confidentiality, intellectual property ownership, non-disparagement, indemnification, governing law, and dispute resolution shall survive any termination or expiration of this Agreement.

## 11. Status as Independent Contractor

The Contributor shall be deemed an independent contractor of the Company. This Agreement shall not create any association, partnership, joint venture, employee, or agency relationship.

## 12. Representations and Warranties

The Contributor represents and warrants that:
(a) Contributor has full authority to enter into this Agreement
(b) Performance will not breach any agreement with a third party
(c) All Work Product will be original and not infringe third party rights
(d) Services will comply with applicable laws and professional standards

## 13. Indemnification

Contributor shall indemnify and hold Company harmless from any claim arising from:
(a) Alleged infringement of third party intellectual property rights
(b) Breach of Contributor's representations and warranties
(c) Negligent or willful misconduct by Contributor

## 14. Entire Agreement

This Agreement expresses the entire agreement between the parties and supersedes all prior understandings regarding the subject matter.

## 15. Governing Law

This Agreement shall be governed by the laws of ${options.governingLaw || 'Delaware'} and any disputes shall be resolved in the courts of ${options.jurisdiction || 'Delaware'}.

## 16. Consent to Jurisdiction

Each party hereby irrevocably consents to the jurisdiction of the courts of ${options.jurisdiction || 'Delaware'} for any action arising out of or relating to this Agreement and waives any objection to venue in such courts.

## 17. Settlement of Disputes

Any dispute arising out of or relating to this Agreement shall be resolved through binding arbitration in accordance with the Commercial Arbitration Rules of the American Arbitration Association, unless both parties agree to court proceedings.

## 18. Severability

If any provision of this Agreement is found to be unenforceable or invalid, that provision shall be limited or eliminated to the minimum extent necessary so that this Agreement shall otherwise remain in full force and effect and enforceable.

IN WITNESS WHEREOF, the parties have executed this Agreement as of the date first written above.

**COMPANY:**

${venture.name}

By: _________________________
Name: ${venture.contributorPools.coreTeam[0]?.email || 'Authorized Representative'}
Title: Authorized Representative
Date: _______________________

**CONTRIBUTOR:**

Name: ${contributor.email}
Role: ${contributor.role}
Date: _______________________
Address: ____________________
____________________________

---

## SCHEDULE A - PROJECT SPECIFICATIONS

**Project:** ${venture.name}
**Description:** ${venture.description || 'As specified in project documentation'}

**Contributor Role:** ${contributor.role}
**Responsibilities:** ${contributor.responsibilities?.join(', ') || 'As defined in project specifications'}

**Revenue Model:** ${isUnifiedPool ? 'Unified Contribution Pool' : 'Separate Pool System'}
**Participation Model:** ${contributor.participationModel || 'tranche_based'}

---

*This agreement is generated based on the lawyer-approved City of Gamers Inc. Contributor Agreement template, adapted for ${isUnifiedPool ? 'unified pool' : 'separate pool'} revenue distribution and dynamic contributor management.*
    `.trim();
  }

  // ID Generators
  generateVentureId() { return 'venture_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9); }
  generateContributorId() { return 'contributor_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9); }
  generateTrancheId() { return 'tranche_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9); }
  generateContributionId() { return 'contribution_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9); }
  generateAgreementId() { return 'agreement_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9); }
}

// Export the dynamic contributor management system
export const dynamicContributorManagement = new DynamicContributorManagement();
