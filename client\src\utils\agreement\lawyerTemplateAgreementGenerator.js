/**
 * Lawyer Template Agreement Generator
 * 
 * Generates professional legal agreements based on the lawyer-approved 
 * City of Gamers Inc. Contributor Agreement template, adapted for 
 * the unified pool system and dynamic contributor management.
 */

import { REVENUE_CALCULATION_METHODS } from './dynamicContributorManagement.js';

export class LawyerTemplateAgreementGenerator {
  constructor() {
    this.generatedAgreements = new Map();
  }

  /**
   * Generate comprehensive agreement based on lawyer-approved template
   */
  generateProfessionalAgreement(venture, contributor, options = {}) {
    const agreement = {
      id: this.generateAgreementId(),
      ventureId: venture.id,
      contributorId: contributor.id,
      
      // Agreement metadata
      templateVersion: '2.0',
      basedOnTemplate: 'City of Gamers Inc. Contributor Agreement',
      generatedAt: new Date().toISOString(),
      effectiveDate: options.effectiveDate || new Date().toISOString(),
      
      // Legal structure
      parties: this.extractParties(venture, contributor),
      terms: this.generateComprehensiveTerms(venture, contributor, options),
      
      // Generated content
      content: '',
      
      // Status
      status: 'generated'
    };

    // Generate the full agreement content
    agreement.content = this.generateFullAgreementContent(agreement, venture, contributor, options);
    
    // Store the agreement
    this.generatedAgreements.set(agreement.id, agreement);
    
    return agreement;
  }

  /**
   * Extract parties information
   */
  extractParties(venture, contributor) {
    return {
      company: {
        name: venture.name,
        type: 'Venture/Project',
        address: 'TBD', // To be determined by user
        representative: venture.contributorPools.coreTeam[0]?.email || 'TBD'
      },
      contributor: {
        email: contributor.email,
        role: contributor.role,
        type: contributor.contributorType || 'gigwork',
        address: 'TBD' // To be filled by contributor
      }
    };
  }

  /**
   * Generate comprehensive terms based on lawyer template
   */
  generateComprehensiveTerms(venture, contributor, options) {
    const isUnifiedPool = venture.revenueModel.calculationMethod === REVENUE_CALCULATION_METHODS.CONTRIBUTION_POINTS;
    
    return {
      // Revenue sharing (adapted for unified pool)
      revenueSharing: {
        method: venture.revenueModel.calculationMethod,
        isUnifiedPool,
        contributorPoolPercentage: venture.revenueModel.gigworkPoolPercentage,
        platformFeePercentage: venture.revenueModel.platformFeePercentage,
        contributionPointsWeight: venture.revenueModel.contributionPointsWeight,
        paymentFrequency: 'tranche_completion',
        minimumPayoutThreshold: 50
      },
      
      // Intellectual property (based on lawyer template)
      intellectualProperty: {
        workProductOwnership: 'company', // Company owns work product
        backgroundIPRetained: true, // Contributor retains background IP
        licenseToCompany: 'nonexclusive_royalty_free', // License background IP to company
        attributionRequired: true,
        ipRights: contributor.ipRights || 'contributor'
      },
      
      // Confidentiality (from lawyer template)
      confidentiality: {
        confidentialityPeriod: options.confidentialityPeriod || 24, // months
        returnOfDocuments: true,
        thirdPartyInformation: true,
        survivesTermination: true
      },
      
      // Participation model
      participation: {
        model: contributor.participationModel || 'tranche_based',
        canJoinTranches: true,
        canLeaveTranches: true,
        requiresApproval: venture.scalabilityConfig?.requireApprovalForNewContributors || false,
        noticePeriod: 7 // days
      },
      
      // Termination (from lawyer template)
      termination: {
        terminationForConvenience: true,
        noticePeriod: 30, // days
        terminationForCause: true,
        curePeriodsForBreach: 30, // days
        effectsOfTermination: 'return_confidential_info'
      },
      
      // Legal provisions (from lawyer template)
      legal: {
        independentContractor: true,
        governingLaw: options.governingLaw || 'Delaware',
        jurisdiction: options.jurisdiction || 'Delaware',
        disputeResolution: 'court', // Can be 'arbitration' or 'court'
        entireAgreement: true,
        severability: true,
        waiverRequiresWriting: true
      },
      
      // Representations and warranties
      representations: {
        authorityToEnter: true,
        noConflicts: true,
        originalWork: true,
        noInfringement: true,
        compliance: true
      },
      
      // Indemnification
      indemnification: {
        contributorIndemnifies: true,
        scopeOfIndemnification: ['ip_infringement', 'breach_of_representations', 'negligence'],
        mutualIndemnification: false
      }
    };
  }

  /**
   * Generate full agreement content based on lawyer template structure
   */
  generateFullAgreementContent(agreement, venture, contributor, options) {
    const { parties, terms } = agreement;
    const effectiveDate = new Date(agreement.effectiveDate).toLocaleDateString();
    
    return `
# ${venture.name.toUpperCase()}
# CONTRIBUTOR AGREEMENT

This Contributor Agreement (this "Agreement") is effective as of ${effectiveDate}, by and between ${parties.company.name}, a collaborative venture (the "Company") and ${parties.contributor.email} (the "Contributor").

## Recitals

WHEREAS, the Company desires to procure services of the Contributor, and the Contributor is willing to provide services to the Company, specifically as provided in the project specifications (the "Services") for the consideration as provided in the revenue sharing model (the "Consideration");

WHEREAS, the Contributor shall provide the Services to the Company as an independent contractor on the terms set forth in this Agreement;

WHEREAS, Contributor to the best of his or her knowledge is not legally obligated, whether by entering into an agreement or otherwise, in any way that conflicts with the terms of this Agreement, nor violates any third party's Intellectual Property Rights; and

WHEREAS, the Contributor acknowledges he or she will have access to certain confidential information which is vital to the success of the Company's business, and that the Company has a legitimate business interest in protecting such information.

NOW THEREFORE, in consideration of the foregoing and for other good and valuable consideration (the receipt of which the Contributor hereby acknowledges), the Contributor, intending to be legally bound hereby, agrees with the Company as follows:

## 1. Definitions

The terms below shall have the following meanings when used throughout this Agreement:

(a) **"Background IP"** means the Intellectual Property Rights owned by or licensed to a Party at the Effective Date, and any modifications or improvements thereto.

(b) **"Confidential Information"** shall mean and include all information, whether written or oral, tangible or intangible, of a private, secret, proprietary or confidential nature, of or concerning the Company, its business and operations, including without limitation, any trade secrets, development information, technology, processes, methodologies, business plans, financial data, and any other information that the Company possesses or uses and not releases externally without restriction.

(c) **"Contribution"** means any original Work Product, including any modification of or addition to an existing work product that the Contributor submits to the Company.

(d) **"Developed IP"** means the intellectual property rights created by or on behalf of Contributor in the course of performing the Services, including all intellectual property in and to the Work Product.

(e) **"Work Product"** shall mean and include any and all products, designs, works, original works, discoveries, inventions and improvements and other results of the Contributor's engagement with the Company that may be conceived, developed, produced, prepared, created or contributed to by the Contributor during the period of engagement.

(f) **"Revenue Tranche"** means a designated portion of Revenue that is allocated for distribution to Contributors based on predefined criteria, including time periods and revenue thresholds.

(g) **"Contribution Points"** means the numerical value assigned to Contributor's Contributions based on factors including time committed, task complexity, and overall impact on the Work Product.

## 2. Revenue Sharing

**Revenue Distribution Model:** ${terms.revenueSharing.isUnifiedPool ? 'Unified Contribution Pool' : 'Separate Pool System'}

(a) **Revenue Pool.** ${terms.revenueSharing.isUnifiedPool ? 
  `All Contributors (including founders) participate in a unified revenue pool representing ${terms.revenueSharing.contributorPoolPercentage}% of total venture revenue. Revenue is distributed based purely on contribution points earned during active participation.` :
  `Contributors participate in a designated pool representing ${terms.revenueSharing.contributorPoolPercentage}% of total venture revenue, separate from core team allocations.`}

(b) **Contribution Points System.** Revenue distribution is calculated based on Contribution Points earned through:
   i. Time committed to project work
   ii. Complexity and difficulty of tasks completed
   iii. Quality of work delivered
   iv. Overall impact on project success

(c) **Payment Frequency.** Revenue distributions occur upon completion of Revenue Tranches, typically aligned with major project milestones or release cycles.

(d) **Minimum Payout.** Minimum payout threshold is $${terms.revenueSharing.minimumPayoutThreshold} per distribution period.

## 3. Treatment of Confidential Information

(a) **Ownership and Implied Rights.** The Contributor acknowledges that all Confidential Information is and shall remain the exclusive property of the Company.

(b) **Use and Disclosure.** The Contributor agrees that at all times during and after termination of engagement, the Contributor shall:
   i. Hold the Confidential Information in the strictest confidence
   ii. Use the Confidential Information solely in connection with engagement with the Company
   iii. Take all precautions necessary to ensure confidentiality
   iv. Observe all security policies implemented by the Company

(c) **Return of Confidential Documents.** The Contributor shall return all Confidential Documents to the Company upon request or termination of engagement.

(d) **Survival.** Confidentiality obligations survive termination for ${terms.confidentiality.confidentialityPeriod} months.

## 4. Ownership of Work Product

(a) **Company Ownership.** All Work Product conceived, created, designed, developed or contributed by the Contributor in connection with providing Services shall be and remain the exclusive property of the Company.

(b) **Background IP License.** If Contributor incorporates Background IP into Company Work Product, the Company is hereby granted a nonexclusive, transferable, royalty-free, fully-paid, irrevocable, perpetual, world-wide license to use such Background IP.

(c) **Assignment of Rights.** The Contributor hereby assigns, transfers and conveys to the Company any and all worldwide right, title and interest in the Work Product.

## 5. Participation Model

(a) **Participation Type.** Contributor participates under the "${terms.participation.model}" model, allowing ${terms.participation.canJoinTranches ? 'flexible joining and leaving of' : 'fixed participation in'} Revenue Tranches.

(b) **Joining/Leaving Tranches.** Contributor may join or leave Revenue Tranches with ${terms.participation.noticePeriod} days notice${terms.participation.requiresApproval ? ', subject to Company approval' : ''}.

(c) **Revenue Entitlement.** Contributor earns revenue only from Tranches in which they actively participate and contribute.

## 6. Termination

(a) **Termination for Convenience.** Either Party may terminate this Agreement on ${terms.termination.noticePeriod} days' written notice.

(b) **Termination for Cause.** Either Party may terminate immediately for material breach not cured within ${terms.termination.curePeriodsForBreach} days of written notice.

(c) **Effects of Termination.** Upon termination, Contributor shall promptly return all Confidential Information and Work Product materials.

## 7. Status as Independent Contractor

The Contributor shall be deemed an independent contractor of the Company. This Agreement shall not create any association, partnership, joint venture, employee, or agency relationship.

## 8. Representations and Warranties

The Contributor represents and warrants that:
(a) Contributor has full authority to enter into this Agreement
(b) Performance will not breach any agreement with a third party
(c) All Work Product will be original and not infringe third party rights
(d) Services will comply with applicable laws and professional standards

## 9. Indemnification

Contributor shall indemnify and hold Company harmless from any claim arising from:
(a) Alleged infringement of third party intellectual property rights
(b) Breach of Contributor's representations and warranties
(c) Negligent or willful misconduct by Contributor

## 10. Governing Law and Jurisdiction

This Agreement shall be governed by the laws of ${terms.legal.governingLaw} and any disputes shall be resolved in the courts of ${terms.legal.jurisdiction}.

## 11. Entire Agreement

This Agreement expresses the entire agreement between the parties and supersedes all prior understandings regarding the subject matter.

## 12. Severability

If any provision of this Agreement is found unenforceable, the remainder shall remain in full force and effect.

IN WITNESS WHEREOF, the parties have executed this Agreement as of the date first written above.

**COMPANY:**

${parties.company.name}

By: _________________________
Name: ${parties.company.representative}
Title: Authorized Representative
Date: _______________________

**CONTRIBUTOR:**

Name: ${parties.contributor.email}
Role: ${parties.contributor.role}
Date: _______________________
Address: ____________________
____________________________

---

## SCHEDULE A - PROJECT SPECIFICATIONS

**Project:** ${venture.name}
**Description:** ${venture.description}

**Contributor Role:** ${contributor.role}
**Responsibilities:** ${contributor.responsibilities?.join(', ') || 'As defined in project specifications'}

**Revenue Model:** ${terms.revenueSharing.isUnifiedPool ? 'Unified Contribution Pool' : 'Separate Pool System'}
**Participation Model:** ${terms.participation.model}

---

*This agreement is generated based on the lawyer-approved City of Gamers Inc. Contributor Agreement template, adapted for ${terms.revenueSharing.isUnifiedPool ? 'unified pool' : 'separate pool'} revenue distribution and dynamic contributor management.*
    `.trim();
  }

  /**
   * Generate unique agreement ID
   */
  generateAgreementId() {
    return 'lawyer_template_agreement_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}

// Export the lawyer template agreement generator
export const lawyerTemplateAgreementGenerator = new LawyerTemplateAgreementGenerator();
