/**
 * Alliance Creation System
 * 
 * Creates and manages collaborative alliances for ventures.
 */

export class AllianceCreationSystem {
  constructor() {
    this.alliances = new Map();
  }

  /**
   * Create a new alliance
   */
  createAlliance(allianceDefinition) {
    const alliance = {
      id: this.generateAllianceId(),
      
      // Basic information
      name: allianceDefinition.name,
      description: allianceDefinition.description,
      industry: allianceDefinition.industry,
      
      // Configuration
      revenueModel: allianceDefinition.revenueModel || 'UNIFIED_POOL',
      ipOwnershipModel: allianceDefinition.ipOwnershipModel || 'CO_OWNERSHIP',
      jurisdiction: allianceDefinition.jurisdiction || 'Delaware',
      currency: allianceDefinition.currency || 'USD',
      
      // Governance
      governanceModel: allianceDefinition.governanceModel || 'DEMOCRATIC',
      votingThreshold: allianceDefinition.votingThreshold || 0.6,
      
      // Revenue sharing
      platformFeePercentage: allianceDefinition.platformFeePercentage || 10,
      allianceRevenueShare: allianceDefinition.allianceRevenueShare || 5,
      
      // Legal framework
      disputeResolution: allianceDefinition.disputeResolution || 'ARBITRATION',
      confidentialityPeriod: allianceDefinition.confidentialityPeriod || 24,
      
      // Members
      foundingMembers: allianceDefinition.foundingMembers || [],
      
      // Status
      status: 'active',
      createdAt: new Date().toISOString()
    };

    this.alliances.set(alliance.id, alliance);
    return alliance;
  }

  /**
   * Generate unique alliance ID
   */
  generateAllianceId() {
    return 'alliance_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }
}

// Export the alliance creation system
export const allianceCreationSystem = new AllianceCreationSystem();
