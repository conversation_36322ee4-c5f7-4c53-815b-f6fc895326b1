/**
 * Generate a sample agreement to show alliance-venture separation
 */

import { NewAgreementGenerator } from './client/src/utils/agreement/newAgreementGenerator.js';
import fs from 'fs';

// Simple template for testing
const template = `# CITY OF GAMERS INC.
# CONTRIBUTOR AGREEMENT

This Contributor Agreement (this "Agreement") is effective as of [Date], by and between City of Gamers Inc., a Florida LLC with its principal place of business at 1205 43rd Street, Suite B, Orlando, Florida 32839 (the "Company") and [Contributor] (the "Contributor").

## Project Description

This project involves development work on "Village of The Ages," a village simulation game where players guide communities through historical progressions and manage resource-based challenges.

## 1. Services

The Contributor shall provide services to the Company for the development of the Work Product.

## SCHEDULE A
### Description of Services

This project involves game development work on "Village of The Ages," a village simulation game where players guide communities through historical progressions and manage resource-based challenges.

## EXHIBIT I
### Technical Specifications

**Project Name:** Village of The Ages

**Project Description:** A village simulation game where players guide communities through historical progressions and manage resource-based challenges

**COMPANY:**

City of Gamers Inc.

By: ______________________
Name: <PERSON><PERSON><PERSON>urnigan
Title: President
Date: ______________________

**CONTRIBUTOR:**

[If an individual]

Name: [Contributor]
Date: ________________________
Address: [Contributor Address]
`;

async function generateSample() {
  console.log('🔄 Generating sample agreement with alliance-venture separation...');
  
  const generator = new NewAgreementGenerator();
  
  // Mock project with alliance
  const project = {
    id: 'test-project',
    name: 'EcoTech Innovations',
    description: 'A platform connecting eco-conscious consumers with sustainable product alternatives using AI recommendations',
    project_type: 'software',
    alliance_id: 'alliance-123',
    team_id: 'alliance-123',
    state: 'California',
    city: 'San Francisco',
    address: '123 Green Tech Way, San Francisco, CA 94105'
  };

  const contributors = [{
    id: 'contrib-1',
    permission_level: 'Owner',
    display_name: 'John Venture Owner',
    email: '<EMAIL>',
    state: 'California',
    city: 'San Francisco'
  }];

  const currentUser = {
    id: 'user-1',
    email: '<EMAIL>',
    user_metadata: { full_name: 'Test Contributor' }
  };

  // Mock the supabase import to return alliance data
  const originalModule = await import('./client/src/utils/supabase/supabase.utils.js');
  
  // Create mock supabase that returns alliance data
  const mockSupabase = {
    from: () => ({
      select: () => ({
        eq: () => ({
          single: () => Promise.resolve({
            data: {
              id: 'alliance-123',
              name: 'Green Tech Alliance LLC',
              description: 'Sustainable technology collective',
              alliance_type: 'established',
              industry: 'Technology',
              team_members: [{
                user_id: 'founder-1',
                role: 'founder',
                users: {
                  id: 'founder-1',
                  email: '<EMAIL>',
                  user_metadata: { full_name: 'Jane Smith' }
                }
              }]
            },
            error: null
          })
        })
      })
    })
  };

  // Temporarily replace supabase
  originalModule.supabase = mockSupabase;

  try {
    const agreement = await generator.generateAgreement(template, project, {
      contributors,
      currentUser,
      fullName: 'Test Contributor'
    });

    // Save the agreement
    const filename = `sample-agreement-${new Date().toISOString().split('T')[0]}.md`;
    
    const metadata = `<!-- SAMPLE AGREEMENT WITH ALLIANCE-VENTURE SEPARATION
Generated: ${new Date().toISOString()}
Alliance (Company): Green Tech Alliance LLC
Venture (Project): EcoTech Innovations
Signer: Jane Smith (Founder)
Location: San Francisco, CA

This shows how the system now properly separates:
- Alliance = Company entity in the agreement
- Venture = Project being worked on
- Alliance founder = Person who signs for company
- Alliance location = Company address
-->

`;

    fs.writeFileSync(filename, metadata + agreement);
    
    console.log(`✅ Sample agreement generated: ${filename}`);
    console.log('\n📋 Key Details:');
    console.log('🏢 Company: Green Tech Alliance LLC (alliance)');
    console.log('🚀 Project: EcoTech Innovations (venture)');
    console.log('✍️  Signer: Jane Smith, Founder');
    console.log('📍 Location: San Francisco, CA');
    console.log('\n📄 Check the file to see how alliance/venture details are used throughout!');

  } catch (error) {
    console.error('❌ Error generating agreement:', error);
  }
}

generateSample();
